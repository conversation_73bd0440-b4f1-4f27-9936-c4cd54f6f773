#!/bin/bash

# List of test classes that need to be updated
classes=(
  "TestAssetWorkflowData"
  "TestBankruptcyData"
  "TestBorrowerWorkflowData"
  "TestContactData"
  "TestCreditPullData"
  "TestCreditScoreData"
  "TestDocumentData"
  "TestDocumentWorkflowData"
  "TestEmploymentWorkflowData"
  "TestFileData"
  "TestIncomeData"
  "TestIncomeWorkflowData"
  "TestLiabilityData"
  "TestLoanAmountData"
  "TestOwnedPropertyData"
  "TestPropertyData"
)

for class in "${classes[@]}"; do
  file="wealthfront-platform/src/test/java/com/wealthfront/branchy/${class}.java"
  echo "Updating $file"
  
  # Replace getInternalId method
  sed -i '' 's/public String getInternalId() {/public TestMortgageDataId getInternalId() {/' "$file"
  sed -i '' 's/return id;/return id != null ? TestMortgageDataId.of(id) : null;/' "$file"
  
  # Replace setInternalId method
  sed -i '' 's/public void setInternalId(String id) {/public void setInternalId(TestMortgageDataId id) {/' "$file"
  sed -i '' 's/this\.id = id;/this.id = id != null ? id.getId() : null;/' "$file"
done

echo "Done updating test classes"
