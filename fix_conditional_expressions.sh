#!/bin/bash

# Fix conditional expressions that mix TestMortgageDataId and String
# These are typically in toString() methods

# Pattern 1: condition ? getInternalId() : null
# Should be: condition ? getInternalId().getId() : null
sed -i '' 's/\? getInternalId() : null/? (getInternalId() != null ? getInternalId().getId() : null) : null/g' wealthfront-platform/src/test/java/com/wealthfront/branchy/*.java

# Pattern 2: condition ? id : null where id is now TestMortgageDataId but should be String
# This is more complex and needs manual fixing

echo "Fixed conditional expressions. Some manual fixes may still be needed."
