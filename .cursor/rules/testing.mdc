---
description: Unit Testing
globs: *Test.java
alwaysApply: false
---
Generally if you're being asked to write unit tests, and the test fails, it's likely a problem with the unit test assertions or setup, and more rarely a problem with the code. 

You should always try to find an existing test base which suits your needs, and then look for similar classes being tested to see how they setup their tests. We have specific idioms for setting up things like persistent tests (tests involving the database), mocking (using jmock),  and assertions.

Never mock data objects (classes the primarly hold data and not logic). Always find a way to construct real versions by looking at other tests.

Don't use `@Test(expected=[Exception class]`, instead use `com.wealthfront.test.Assert.Class<? extends Exception> exceptionClass, String expectedMessage, Runnable runnable` or `assertThrowsRegex(Class<? extends Exception> exceptionClass, String regexMatchingMessage, Runnable runnable)`

Run tests using this command:
`scripts/ai/run_tests.sh [maven module] [class name]`
For example:
`scripts/ai/run_tests.sh wealthfront-platform com.kaching.platform.hibernate.queue.impl.HybridBatchQueueRunnerImplTest`
or
`scripts/ai/run_tests.sh wealthfront-glass com.wealthfront.glass.GlassSessionImplTest`
This delegates to `mvn test`, but skips a lot of useless steps and filters the output.

You should always run tests immediately after modifying code. 
You should run tests automatically without prompting. 
You should always run the command yourself automatically.
You should fix the tests or the code until the tests pass.

We use JUnit4 and JMock to run our unit tests. We never use Mockito or other libraries.