---
description: Java Style Guide
globs: *.java
---
Do not write any comments. Comments are only appropriate on interfaces that explain promised behavior in ways that can't be expressed through the type system or better type/method naming.

Do statically import redundant names that don't add much useful information for the reader:
Money.money(13) → money(13)
CostBasisEntityFactory.createExpectedLot() → createExpectedLot()
.collect(Collectors.toList()) → .collect(toList())
Preconditions.checkArgument() → checkArgument

Do NOT statically import non-descriptive methods:
Option.some("something"), Option.none()
ImmutableList.of(), ImmutableSet.of()
BigDecimal.valueOf(), Long.valueOf(),
Strings.format(), String.format()