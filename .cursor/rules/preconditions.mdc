---
description: Production code preconditions and assertions
globs: 
---
# Google Preconditions
Use com.google.common.base.Preconditions liberally. These methods should always be statically imported:
Use `static <T> T checkNotNull(T reference, "optional error template", Object... templateArgs)` to assert that the input or return value of a method isn't null, a Map value isn't null, etc.
Use `static void checkArgument(boolean value, "optional error template", Object... templateArgs)` to assert the well-formedness of input to a method. i.e. if a problem is the caller's fault.
Use `static void checkState(boolean value, "optional error template", Object... templateArgs)` to assert that a system's existing state is as expected. 

# Query Preconditions
However, if code is running in the context of an [AbstractQuery.java](mdc:wealthfront-platform/src/main/java/com/kaching/platform/queryengine/AbstractQuery.java), which is the endpoint of an RPC framework, com.kaching.util.Preconditions should be used instead. These methods should also always be statically imported.

Use `static <T> T checkFound(T value, String errorMessageTemplate, Object... args)` or throw a [NotFoundException.java](mdc:wealthfront-platform/src/main/java/com/kaching/platform/queryengine/exceptions/NotFoundException.java) when an AbstractQuery is given some identifier that doesn't exist or a user doesn't have access to. This translates to an HTTP 404 code on the frontend.

Use `static void checkQueryArgument(boolean expression, String errorMessageTemplate, Object... args)` or throw a `com.kaching.platform.queryengine.exceptions.InvalidArgumentException(String message, Object... args)` when asserting well-formedness of query inputs. These translate to a HTTP 400 code on the frontend.




