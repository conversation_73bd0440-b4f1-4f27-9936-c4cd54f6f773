package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.API_SERVER
)
public enum TestEmploymentStatus {
  ONE_JOB,

  MULTIPLE_JOBS,

  UNEMPLOYED,

  RETIRED;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case ONE_JOB:
        return visitor.caseOneJob();
      case MULTIPLE_JOBS:
        return visitor.caseMultipleJobs();
      case UNEMPLOYED:
        return visitor.caseUnemployed();
      case RETIRED:
        return visitor.caseRetired();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseOneJob();

    T caseMultipleJobs();

    T caseUnemployed();

    T caseRetired();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseOneJob() {
      return defaultValue.get();
    }

    @Override
    public T caseMultipleJobs() {
      return defaultValue.get();
    }

    @Override
    public T caseUnemployed() {
      return defaultValue.get();
    }

    @Override
    public T caseRetired() {
      return defaultValue.get();
    }
  }
}
