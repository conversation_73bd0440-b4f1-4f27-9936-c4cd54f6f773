package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.API_SERVER
)
public enum TestBankruptcyChapterType {
  CHAPTER_SEVEN,

  CHAPTER_ELEVEN,

  CHAPTER_TWELVE,

  CHAPTER_THIRTEEN;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case CHAPTER_SEVEN:
        return visitor.caseChapterSeven();
      case CHAPTER_ELEVEN:
        return visitor.caseChapterEleven();
      case CHAPTER_TWELVE:
        return visitor.caseChapterTwelve();
      case CHAPTER_THIRTEEN:
        return visitor.caseChapterThirteen();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseChapterSeven();

    T caseChapterEleven();

    T caseChapterTwelve();

    T caseChapterThirteen();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseChapterSeven() {
      return defaultValue.get();
    }

    @Override
    public T caseChapterEleven() {
      return defaultValue.get();
    }

    @Override
    public T caseChapterTwelve() {
      return defaultValue.get();
    }

    @Override
    public T caseChapterThirteen() {
      return defaultValue.get();
    }
  }
}
