package com.kaching.branchy;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;

import java.io.IOException;
import java.util.List;

import org.junit.Before;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.kaching.api.CodeGenTestBase;
import com.kaching.api.GlobalEntityNamespaceTransformer;
import com.kaching.api.JavaApiCodeGenerator;
import com.kaching.api.JavaApiValidator;
import com.kaching.fs.ExposeEntitiesClassLoader;
import com.squareup.javapoet.JavaFile;
import com.wealthfront.branchy.TestBorrowerData;

public class JavaBranchyDataModelGeneratorTest extends CodeGenTestBase {

  @Before
  public void before() throws IOException {
    super.before();
  }

  @Test
  public void main_invalidArguments() throws IOException {
    try {
      JavaBranchyDataModelGenerator.main("buildDirectory1", "buildDirectory2");
      fail();
    } catch (IllegalArgumentException e) {
      assertEquals("exactly one argument required (build directory)", e.getMessage());
    }
  }

  @Test
  public void generate() throws IOException {
    JavaBranchyDataModelGenerator generator = getGenerator();

    generator.generate();

    List<String> allFiles = getAllNewFiles(fakeFs);

    assertEquals(ImmutableList.of(

    ), allFiles);

  }

  @Test
  public void generateBorrowerData() {
    JavaBranchyDataModelGenerator generator = getGenerator();

    List<JavaFile> generatedFiles = generator.generateEntities(ImmutableList.of(TestBorrowerData.class));

    assertGeneratedFilesEqual(getClass(), ImmutableList.of(
        "JavaBranchyDataModelGenerator_TestAddressData.java.txt",
        "JavaBranchyDataModelGenerator_TestBankruptcyChapterType.java.txt",
        "JavaBranchyDataModelGenerator_TestBankruptcyData.java.txt",
        "JavaBranchyDataModelGenerator_TestBorrowerData.java.txt",
        "JavaBranchyDataModelGenerator_TestBorrowerType.java.txt",
        "JavaBranchyDataModelGenerator_TestBorrowerWorkflowData.java.txt",
        "JavaBranchyDataModelGenerator_TestCitizenshipType.java.txt",
        "JavaBranchyDataModelGenerator_TestCreditBureau.java.txt",
        "JavaBranchyDataModelGenerator_TestCreditPullConsentType.java.txt",
        "JavaBranchyDataModelGenerator_TestCreditPullStatus.java.txt",
        "JavaBranchyDataModelGenerator_TestCreditPullType.java.txt",
        "JavaBranchyDataModelGenerator_TestCreditScoreData.java.txt",
        "JavaBranchyDataModelGenerator_TestCreditScoreUnavailableReason.java.txt",
        "JavaBranchyDataModelGenerator_TestEmploymentStatus.java.txt",
        "JavaBranchyDataModelGenerator_TestEntityAction.java.txt",
        "JavaBranchyDataModelGenerator_TestIncomeWorkflowData.java.txt",
        "JavaBranchyDataModelGenerator_TestMaritalStatus.java.txt",
        "JavaBranchyDataModelGenerator_TestMilitaryStatus.java.txt",
        "JavaBranchyDataModelGenerator_TestOccupancyBasis.java.txt"
    ), generatedFiles);

    assertJavaFilesCompile(generatedFiles);
  }

  private JavaBranchyDataModelGenerator getGenerator() {
    JavaBranchyDataModelGenerator generator = new JavaBranchyDataModelGenerator("example");
    Injector injector = Guice.createInjector();
    generator.fs = fakeFs;
    generator.javaApiCodeGenerator = injector.getInstance(JavaApiCodeGenerator.class);
    generator.exposeEntitiesClassLoader = injector.getInstance(ExposeEntitiesClassLoader.class);
    generator.namespaceTransformer = injector.getInstance(GlobalEntityNamespaceTransformer.class);
    generator.javaApiValidator = injector.getInstance(JavaApiValidator.class);
    return generator;
  }

}