package com.wealthfront.branchy;

import java.util.Collections;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
@ExposeType(value = { ExposeTo.API_SERVER }, namespace = ExposeType.RewriteNamespace.SERVICE)
class TestFileData implements TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private TestEntityAction action;

  @Value
  private String id;

  @Value(optional = true)
  private String s3Path;

  @Value(optional = true)
  @TestNoopVestaSync
  private String header;

  @Value(optional = true)
  private String filename;

  @Value(optional = true)
  private State state;

  TestFileData() { /* JSON */ }

  TestFileData(TestEntityAction entityAction, String id, String s3Path, String header, String filename, State state) {
    this.action = entityAction;
    this.id = id != null ? id : null;
    this.s3Path = s3Path;
    this.header = header;
    this.filename = filename;
    this.state = state;
  }

  public String getId() {
    return id != null ? TestMortgageDataId.of(id) : null;
  }

  public Option<String> getS3Path() {
    return Option.of(s3Path);
  }

  public Option<String> getHeader() {
    return Option.of(header);
  }

  public Option<String> getFilename() {
    return Option.of(filename);
  }

  public Option<State> getState() {
    return Option.of(state);
  }

  @Override
  public TestMortgageDataId getInternalId() {
    return id != null ? TestMortgageDataId.of(id) : null;
  }

  @Override
  public void setInternalId(TestMortgageDataId id) {
    this.id = id != null ? id : null;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.FILE;
  }

  @Override
  public <T> T visit(TestMortgageDataVisitor<T> mortgageDataVisitor) {
    return mortgageDataVisitor.caseFileData(this);
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    if (state == null) {
      return TestVestaOperationType.NO_OP;
    }

    return switch (state) {
      case UPLOADING, FAILED -> TestVestaOperationType.NO_OP;
      case UPLOADED -> TestVestaOperationType.DOCUMENT_UPLOAD;
    };
  }

  public enum State {
    UPLOADING,
    UPLOADED,
    FAILED;
  }

}
