package com.wealthfront.branchy;

import java.util.Collections;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
@ExposeType(value = { ExposeTo.API_SERVER }, namespace = ExposeType.RewriteNamespace.SERVICE)
class TestAddressData implements TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional=true)
  private String id;

  @Value(optional=true)
  private String line1;

  @Value(optional=true)
  private String line2;

  @Value(optional=true)
  private String city;

  @Value(optional=true)
  private String state;

  @Value(optional=true)
  private String county;

  @Value(optional=true)
  private String postalCode;

  @Value(optional=true)
  private String country;

  @Value(optional=true)
  private TestOccupancyBasis occupancyBasis;

  TestAddressData() { /* JSON */ }

  TestAddressData(TestEntityAction action,
                     String line1,
                     String line2,
                     String city,
                     String state,
                     String county,
                     String postalCode,
                     String country,
      TestOccupancyBasis occupancyBasis) {
    this.action = action;
    this.line1 = line1;
    this.line2 = line2;
    this.city = city;
    this.state = state;
    this.county = county;
    this.postalCode = postalCode;
    this.country = country;
    this.occupancyBasis = occupancyBasis;
  }

  public Option<String> getLine1() {
    return Option.of(line1);
  }

  public Option<String> getLine2() {
    return Option.of(line2);
  }

  public Option<String> getCity() {
    return Option.of(city);
  }

  public Option<String> getState() {
    return Option.of(state);
  }

  public Option<String> getCounty() {
    return Option.of(county);
  }

  public Option<String> getPostalCode() {
    return Option.of(postalCode);
  }

  public Option<String> getCountry() {
    return Option.of(country);
  }

  public Option<TestOccupancyBasis> getOccupancyBasis() {
    return Option.of(occupancyBasis);
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.ADDRESS;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

  @Override
  public <T> T visit(TestMortgageDataVisitor<T> mortgageDataVisitor) {
    return mortgageDataVisitor.caseAddressData(this);
  }

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  @Override
  public TestMortgageDataId getInternalId() {
    return id != null ? TestMortgageDataId.of(id) : null;
  }

  @Override
  public void setInternalId(TestMortgageDataId id) {
    this.id = id != null ? id.getId() : null;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

}
