package com.wealthfront.branchy;

import java.util.Collections;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.entities.EmailAddress;
import com.kaching.entities.PhoneNumber;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
@ExposeType(value = { ExposeTo.API_SERVER }, namespace = ExposeType.RewriteNamespace.SERVICE)
class TestContactData implements TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional=true)
  private TestEntityAction action;

  @Value(optional=true)
  private String id;

  @Value(optional=true)
  private TestContactType contactType;

  @Value(optional=true)
  private String firstName;

  @Value(optional=true)
  private String lastName;

  @Value(optional=true)
  private EmailAddress emailAddress;

  @Value(optional=true)
  private PhoneNumber phoneNumber;

  TestContactData() { /* JSON */ }

  TestContactData(
      TestEntityAction action, String id, TestContactType contactType, String firstName, String lastName, EmailAddress emailAddress,
      PhoneNumber phoneNumber) {
    this.action = action;
    this.id = id != null ? id : null;
    this.contactType = contactType;
    this.firstName = firstName;
    this.lastName = lastName;
    this.emailAddress = emailAddress;
    this.phoneNumber = phoneNumber;
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.LOAN;
  }

  @Override
  public <T> T visit(TestMortgageDataVisitor<T> mortgageDataVisitor) {
    return mortgageDataVisitor.caseContactData(this);
  }

  public String getId() {
    return id != null ? TestMortgageDataId.of(id) : null;
  }

  public Option<TestContactType> getContactType() {
    return Option.of(contactType);
  }

  public Option<String> getFirstName() {
    return Option.of(firstName);
  }

  public Option<String> getLastName() {
    return Option.of(lastName);
  }

  public Option<EmailAddress> getEmailAddress() {
    return Option.of(emailAddress);
  }

  public Option<PhoneNumber> getPhoneNumber() {
    return Option.of(phoneNumber);
  }

  @Override
  public TestMortgageDataId getInternalId() {
    return getId();
  }

  @Override
  public void setInternalId(TestMortgageDataId id) {
    this.id = id != null ? id : null;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return this.fieldsToNullSet == null ? Collections.emptySet() : this.fieldsToNullSet;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

}
