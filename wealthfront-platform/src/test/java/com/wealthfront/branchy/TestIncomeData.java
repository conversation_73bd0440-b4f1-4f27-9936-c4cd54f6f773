package com.wealthfront.branchy;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Set;

import org.joda.time.LocalDate;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
@ExposeType(value = {ExposeTo.API_SERVER}, namespace = ExposeType.RewriteNamespace.SERVICE)
class TestIncomeData implements TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private String id;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private String borrowerId;

  @Value(optional = true)
  private TestEmploymentWorkflowData employmentWorkflowData;

  @Value(optional = true)
  private TestIncomeType incomeType;

  @Value(optional = true)
  private String employerName;

  @Value(optional = true)
  private Boolean isPrimaryEmployer;

  @Value(optional = true)
  private LocalDate startDate;

  @Value(optional = true)
  private TestAddressData employerAddress;

  @Value(optional = true)
  private BigDecimal employerOwnershipPercentage;

  @Value(optional = true)
  private Boolean hasTakenLeave;

  @Value(optional = true)
  private Boolean isEmployerCannabisIndustry;

  @Value(optional = true)
  private Boolean isEmployerFamilyOwned;

  @Value(optional = true)
  private TestPay base;

  @Value(optional = true)
  private TestPay averageCommission;

  @Value(optional = true)
  private TestPay overtime;

  @Value(optional = true)
  private TestPay bonus;

  @Value(optional = true)
  private TestPay restrictedStockPay;

  @Value(optional = true)
  private TestPay tips;

  @Value(optional = true)
  private TestPay militaryClothesAllowance;

  @Value(optional = true)
  private TestPay militaryCombatPay;

  @Value(optional = true)
  private TestPay militaryFlightPay;

  @Value(optional = true)
  private TestPay militaryHazardPay;

  @Value(optional = true)
  private TestPay militaryOverseasPay;

  @Value(optional = true)
  private TestPay militaryPropPay;

  @Value(optional = true)
  private TestPay militaryQuartersAllowance;

  @Value(optional = true)
  private TestPay militaryRationsAllowance;

  @Value(optional = true)
  private TestPay militaryVariableHousingAllowance;

  @Value(optional = true)
  private TestSelfEmploymentBusinessType selfEmploymentBusinessType;

  @Value(optional = true)
  private TestPay nonEmploymentIncomeAmount;

  @Value(optional = true)
  private Boolean incomeContinuance;

  @Value(optional = true)
  private Boolean hasSufficientIncomeHistory;

  @Value(optional = true)
  private Boolean hasFutureEmployment;

  @Value(optional = true)
  private TestBonusHistoryRange bonusIncomeHistory;

  TestIncomeData() { /* JSON */ }

  TestIncomeData(
      TestEntityAction action,
      String borrowerId,
      TestEmploymentWorkflowData employmentWorkflowData, TestIncomeType incomeType,
      String employerName, Boolean isPrimaryEmployer, LocalDate startDate, TestAddressData employerAddress, BigDecimal employerOwnershipPercentage,
      Boolean hasTakenLeave, Boolean isEmployerCannabisIndustry, Boolean isEmployerFamilyOwned, TestPay base,
      TestPay.BasicPay averageCommission, TestPay.BasicPay overtime, TestPay.BasicPay bonus, TestPay.RestrictedStockPay restrictedStockPay,
      TestPay.BasicPay tips,
      TestPay.BasicPay militaryClothesAllowance, TestPay.BasicPay militaryCombatPay, TestPay.BasicPay militaryFlightPay, TestPay.BasicPay militaryHazardPay,
      TestPay.BasicPay militaryOverseasPay, TestPay.BasicPay militaryPropPay, TestPay.BasicPay militaryQuartersAllowance, TestPay.BasicPay militaryRationsAllowance,
      TestPay.BasicPay militaryVariableHousingAllowance, TestSelfEmploymentBusinessType selfEmploymentBusinessType,
      TestPay.BasicPay nonEmploymentIncomeAmount, Boolean incomeContinuance, Boolean hasSufficientIncomeHistory,
      Boolean hasFutureEmployment, TestBonusHistoryRange bonusIncomeHistory) {
    this.action = action;
    this.borrowerId = borrowerId;
    this.employmentWorkflowData = employmentWorkflowData;
    this.incomeType = incomeType;
    this.employerName = employerName;
    this.isPrimaryEmployer = isPrimaryEmployer;
    this.startDate = startDate;
    this.employerAddress = employerAddress;
    this.employerOwnershipPercentage = employerOwnershipPercentage;
    this.hasTakenLeave = hasTakenLeave;
    this.isEmployerCannabisIndustry = isEmployerCannabisIndustry;
    this.isEmployerFamilyOwned = isEmployerFamilyOwned;
    this.base = base;
    this.averageCommission = averageCommission;
    this.overtime = overtime;
    this.bonus = bonus;
    this.restrictedStockPay = restrictedStockPay;
    this.tips = tips;
    this.militaryClothesAllowance = militaryClothesAllowance;
    this.militaryCombatPay = militaryCombatPay;
    this.militaryFlightPay = militaryFlightPay;
    this.militaryHazardPay = militaryHazardPay;
    this.militaryOverseasPay = militaryOverseasPay;
    this.militaryPropPay = militaryPropPay;
    this.militaryQuartersAllowance = militaryQuartersAllowance;
    this.militaryRationsAllowance = militaryRationsAllowance;
    this.militaryVariableHousingAllowance = militaryVariableHousingAllowance;
    this.selfEmploymentBusinessType = selfEmploymentBusinessType;
    this.nonEmploymentIncomeAmount = nonEmploymentIncomeAmount;
    this.incomeContinuance = incomeContinuance;
    this.hasSufficientIncomeHistory = hasSufficientIncomeHistory;
    this.hasFutureEmployment = hasFutureEmployment;
    this.bonusIncomeHistory = bonusIncomeHistory;
  }

  public TestEntityAction getAction() {
    return action;
  }

  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.INCOME;
  }

  @Override
  public <T> T visit(TestMortgageDataVisitor<T> mortgageDataVisitor) {
    return mortgageDataVisitor.caseIncomeData(this);
  }

  public String getId() {
    return id != null ? TestMortgageDataId.of(id) : null;
  }

  public Option<String> getBorrowerId() {
    return Option.of(borrowerId);
  }

  public Option<TestEmploymentWorkflowData> getEmploymentWorkflowData() {
    return Option.of(employmentWorkflowData);
  }

  public Option<TestIncomeType> getIncomeType() {
    return Option.of(incomeType);
  }

  public Option<String> getEmployerName() {
    return Option.of(employerName);
  }

  public Option<Boolean> getPrimaryEmployer() {
    return Option.of(isPrimaryEmployer);
  }

  public Option<LocalDate> getStartDate() {
    return Option.of(startDate);
  }

  public Option<TestAddressData> getEmployerAddress() {
    return Option.of(employerAddress);
  }

  public Option<BigDecimal> getEmployerOwnershipPercentage() {
    return Option.of(employerOwnershipPercentage);
  }

  public Option<Boolean> getHasTakenLeave() {
    return Option.of(hasTakenLeave);
  }

  public Option<Boolean> getEmployerCannabisIndustry() {
    return Option.of(isEmployerCannabisIndustry);
  }

  public Option<Boolean> getEmployerFamilyOwned() {
    return Option.of(isEmployerFamilyOwned);
  }

  public Option<TestPay> getBase() {
    return Option.of(base);
  }

  public Option<TestPay> getAverageCommission() {
    return Option.of(averageCommission);
  }

  public Option<TestPay> getOvertime() {
    return Option.of(overtime);
  }

  public Option<TestPay> getBonus() {
    return Option.of(bonus);
  }

  public Option<TestPay> getRestrictedStockPay() {
    return Option.of(restrictedStockPay);
  }

  public Option<TestPay> getTips() {
    return Option.of(tips);
  }

  public Option<TestPay> getMilitaryClothesAllowance() {
    return Option.of(militaryClothesAllowance);
  }

  public Option<TestPay> getMilitaryCombatPay() {
    return Option.of(militaryCombatPay);
  }

  public Option<TestPay> getMilitaryFlightPay() {
    return Option.of(militaryFlightPay);
  }

  public Option<TestPay> getMilitaryHazardPay() {
    return Option.of(militaryHazardPay);
  }

  public Option<TestPay> getMilitaryOverseasPay() {
    return Option.of(militaryOverseasPay);
  }

  public Option<TestPay> getMilitaryPropPay() {
    return Option.of(militaryPropPay);
  }

  public Option<TestPay> getMilitaryQuartersAllowance() {
    return Option.of(militaryQuartersAllowance);
  }

  public Option<TestPay> getMilitaryRationsAllowance() {
    return Option.of(militaryRationsAllowance);
  }

  public Option<TestPay> getMilitaryVariableHousingAllowance() {
    return Option.of(militaryVariableHousingAllowance);
  }

  public Option<TestSelfEmploymentBusinessType> getSelfEmploymentBusinessType() {
    return Option.of(selfEmploymentBusinessType);
  }

  public Option<TestPay> getNonEmploymentIncomeAmount() {
    return Option.of(nonEmploymentIncomeAmount);
  }

  public Option<Boolean> getIncomeContinuance() {
    return Option.of(incomeContinuance);
  }

  public Option<Boolean> getHasSufficientIncomeHistory() {
    return Option.of(hasSufficientIncomeHistory);
  }

  public Option<Boolean> getHasFutureEmployment() {
    return Option.of(hasFutureEmployment);
  }

  public Option<TestBonusHistoryRange> getBonusIncomeHistory() {
    return Option.of(bonusIncomeHistory);
  }

  @Override
  public TestMortgageDataId getInternalId() {
    return id != null ? TestMortgageDataId.of(id) : null;
  }

  @Override
  public void setInternalId(TestMortgageDataId id) {
    this.id = id != null ? id : null;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

}