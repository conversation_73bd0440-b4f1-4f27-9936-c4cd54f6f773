package com.wealthfront.branchy;

public interface TestMortgageDataVisitor<T> {
  T caseAddressData(TestAddressData testAddressData);
  
  T caseApplicationData(TestApplicationData testApplicationData);
  
  T caseAssetData(TestAssetData testAssetData);
  
  T caseAssetWorkflowData(TestAssetWorkflowData testAssetWorkflowData);
  
  T caseBankruptcyData(TestBankruptcyData testBankruptcyData);
  
  T caseBorrowerData(TestBorrowerData testBorrowerData);
  
  T caseBorrowerWorkflowData(TestBorrowerWorkflowData testBorrowerWorkflowData);
  
  T caseContactData(TestContactData testContactData);
  
  T caseCreditPull(TestCreditPullData testCreditPullData);
  
  T caseCreditScoreData(TestCreditScoreData testCreditScoreData);
  
  T caseDocumentData(TestDocumentData testDocumentData);
  
  T caseDocumentWorkflowData(TestDocumentWorkflowData testDocumentWorkflowData);
  
  T caseEmploymentWorkflowData(TestEmploymentWorkflowData testEmploymentWorkflowData);
  
  T caseFileData(TestFileData testFileData);
  
  T caseIncomeData(TestIncomeData testIncomeData);
  
  T caseIncomeWorkflowData(TestIncomeWorkflowData testIncomeWorkflowData);
  
  T caseLiabilityData(TestLiabilityData testLiabilityData);
  
  T caseLoanAmountData(TestLoanAmountData testLoanAmountData);
  
  T caseOwnedPropertyData(TestOwnedPropertyData testOwnedPropertyData);
  
  T casePropertyData(TestPropertyData testPropertyData);
}
