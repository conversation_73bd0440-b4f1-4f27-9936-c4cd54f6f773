package com.wealthfront.branchy;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
@ExposeType(value = {ExposeTo.API_SERVER}, namespace = ExposeType.RewriteNamespace.SERVICE)
class TestCreditPullData implements TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private String id;

  @Deprecated // TODO: Remove this field once FLEND is decommissioned (LEND-1211)
  @Value(optional = true)
  private List<TestBorrowerData> borrowers;

  @Value(optional = true)
  private Boolean pullEquifax;

  @Value(optional = true)
  private Boolean pullExperian;

  @Value(optional = true)
  private Boolean pullTransUnion;

  @Value(optional = true)
  private TestCreditPullType creditPullType;

  @Value(optional = true)
  private TestCreditPullRequestType creditPullRequestType;

  @Value(optional = true)
  private TestCreditPullActionType creditPullActionType;

  @Value(optional = true)
  private TestCreditPullSyncStage creditPullSyncStage;

  @Value(optional = true)
  private Boolean syncFailed;

  @Value(optional = true)
  private List<String> borrowerIds;

  TestCreditPullData() {
    /* JSON */
  }

  TestCreditPullData(
      TestEntityAction action, String id, List<TestBorrowerData> borrowers, Boolean pullEquifax,
      Boolean pullExperian,
      Boolean pullTransUnion, TestCreditPullType creditPullType, TestCreditPullRequestType creditPullRequestType,
      TestCreditPullActionType creditPullActionType,
      TestCreditPullSyncStage creditPullSyncStage, Boolean syncFailed, List<String> borrowerIds) {
    this.action = action;
    this.id = id != null ? id : null;
    this.borrowers = borrowers;
    this.pullEquifax = pullEquifax;
    this.pullExperian = pullExperian;
    this.pullTransUnion = pullTransUnion;
    this.creditPullType = creditPullType;
    this.creditPullRequestType = creditPullRequestType;
    this.creditPullActionType = creditPullActionType;
    this.creditPullSyncStage = creditPullSyncStage;
    this.syncFailed = syncFailed;
    this.borrowerIds = borrowerIds;
  }

  public Option<Boolean> isPullEquifax() {
    return Option.of(pullEquifax);
  }

  public Option<Boolean> isPullExperian() {
    return Option.of(pullExperian);
  }

  public Option<Boolean> isPullTransUnion() {
    return Option.of(pullTransUnion);
  }

  public Option<TestCreditPullType> getCreditPullType() {
    return Option.of(creditPullType);
  }

  public Option<TestCreditPullRequestType> getCreditPullRequestType() {
    return Option.of(creditPullRequestType);
  }

  public Option<TestCreditPullActionType> getCreditPullActionType() {
    return Option.of(creditPullActionType);
  }

  public Option<TestCreditPullSyncStage> getCreditPullSyncStage() {
    return Option.of(creditPullSyncStage);
  }

  public Option<List<String>> getBorrowerIds() {
    return Option.of(borrowerIds);
  }

  public Option<Boolean> getSyncFailed() {
    return Option.of(syncFailed);
  }

  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.CREDIT_PULL;
  }

  @Override
  public <T> T visit(TestMortgageDataVisitor<T> mortgageDataVisitor) {
    return mortgageDataVisitor.caseCreditPull(this);
  }

  @Override
  public TestMortgageDataId getInternalId() {
    return id != null ? TestMortgageDataId.of(id) : null;
  }

  @Override
  public void setInternalId(TestMortgageDataId id) {
    this.id = id != null ? id : null;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    if (action == TestEntityAction.ADD) {
      return TestVestaOperationType.CREDIT_PULL;
    }
    return TestVestaOperationType.NO_OP;
  }

}