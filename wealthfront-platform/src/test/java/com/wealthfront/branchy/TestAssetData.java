package com.wealthfront.branchy;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.entities.Money;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
@ExposeType(value = {ExposeTo.API_SERVER}, namespace = ExposeType.RewriteNamespace.SERVICE)
class TestAssetData implements TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private String id;

  @Value(optional = true)
  private TestAssetType assetType;

  @Value(optional = true)
  private Money assetValue;

  @Value(optional = true)
  private String assetDescription;

  @Value(optional = true)
  private String institutionName;

  @Value(optional = true)
  private String accountNumber;

  @Value(optional = true)
  private TestGiftSource giftSource;

  @Value(optional = true)
  private List<String> borrowerIds;

  @Value(optional = true)
  private TestAssetWorkflowData assetWorkflowData;

  TestAssetData() { /* JSON */ }

  TestAssetData(TestEntityAction action, TestAssetType assetType, Money assetValue, String assetDescription,
                   String institutionName, String accountNumber, TestGiftSource giftSource,
                   List<String> borrowerIds, TestAssetWorkflowData assetWorkflowData) {
    this.action = action;
    this.assetType = assetType;
    this.assetDescription = assetDescription;
    this.institutionName = institutionName;
    this.assetValue = assetValue;
    this.accountNumber = accountNumber;
    this.giftSource = giftSource;
    this.borrowerIds = borrowerIds;
    this.assetWorkflowData = assetWorkflowData;
  }

  @Override
  public TestMortgageDataId getInternalId() {
    return id != null ? TestMortgageDataId.of(id) : null;
  }

  @Override
  public void setInternalId(TestMortgageDataId id) {
    this.id = id != null ? id : null;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.ASSET;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

  @Override
  public <T> T visit(TestMortgageDataVisitor<T> mortgageDataVisitor) {
    return mortgageDataVisitor.caseAssetData(this);
  }

  public Option<TestAssetType> getAssetType() {
    return Option.of(assetType);
  }

  public Option<Money> getAssetValue() {
    return Option.of(assetValue);
  }

  public Option<String> getAssetDescription() {
    return Option.of(assetDescription);
  }

  public Option<String> getInstitutionName() {
    return Option.of(institutionName);
  }

  public Option<String> getAccountNumber() {
    return Option.of(accountNumber);
  }

  public Option<TestGiftSource> getGiftSource() {
    return Option.of(giftSource);
  }

  public Option<List<String>> getBorrowerIds() {
    return Option.of(borrowerIds);
  }

  public Option<TestAssetWorkflowData> getAssetWorkflowData() {
    return Option.of(assetWorkflowData);
  }

}
