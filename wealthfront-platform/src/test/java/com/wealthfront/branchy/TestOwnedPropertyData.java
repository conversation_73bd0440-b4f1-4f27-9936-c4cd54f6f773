package com.wealthfront.branchy;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.entities.Money;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity
@ExposeType(value = {ExposeTo.API_SERVER}, namespace = ExposeType.RewriteNamespace.SERVICE)
class TestOwnedPropertyData implements TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private String id;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private List<String> ownerBorrowerIds;

  @Value(optional = true)
  private TestAddressData address;

  @Value(optional = true)
  private TestPropertyType propertyType;

  @Value(optional = true)
  private TestPropertyUsageType currentUsage;

  @Value(optional = true)
  private Money estimatedValue;

  @Value(optional = true)
  private Integer numberOfUnits;

  @Value(optional = true)
  private TestOwnedPropertyDisposition disposition;

  @Value(optional = true)
  private TestRentalIncomeType rentalIncomeType;

  @Value(optional = true)
  private List<String> incomeIds;

  @Value(optional = true)
  private Money monthlyInsurancePremiums;

  @Value(optional = true)
  private Money monthlyPropertyTaxes;

  @Value(optional = true)
  private Money monthlyHoaDues;

  @Value(optional = true)
  private List<String> liabilityIds;

  @Value(optional = true)
  private Boolean ownershipSharedWithNonBorrower;

  TestOwnedPropertyData() { /* JSON */ }

  TestOwnedPropertyData(
      String id, TestEntityAction action, List<String> ownerBorrowerIds, TestAddressData address, TestPropertyType propertyType,
      TestPropertyUsageType currentUsage,
      Money estimatedValue, Integer numberOfUnits, TestOwnedPropertyDisposition disposition,
      TestRentalIncomeType rentalIncomeType, Money monthlyInsurancePremiums,
      Money monthlyPropertyTaxes, Money monthlyHoaDues, List<String> incomeIds,
      List<String> liabilityIds, Boolean ownershipSharedWithNonBorrower) {
    this.id = id != null ? id : null;
    this.action = action;
    this.ownerBorrowerIds = ownerBorrowerIds;
    this.address = address;
    this.propertyType = propertyType;
    this.currentUsage = currentUsage;
    this.estimatedValue = estimatedValue;
    this.numberOfUnits = numberOfUnits;
    this.disposition = disposition;
    this.rentalIncomeType = rentalIncomeType;
    this.monthlyInsurancePremiums = monthlyInsurancePremiums;
    this.monthlyPropertyTaxes = monthlyPropertyTaxes;
    this.monthlyHoaDues = monthlyHoaDues;
    this.incomeIds = incomeIds;
    this.liabilityIds = liabilityIds;
    this.ownershipSharedWithNonBorrower = ownershipSharedWithNonBorrower;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.OWNED_PROPERTY;
  }

  @Override
  public <T> T visit(TestMortgageDataVisitor<T> mortgageDataVisitor) {
    return mortgageDataVisitor.caseOwnedPropertyData(this);
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  public String getId() {
    return id != null ? TestMortgageDataId.of(id) : null;
  }

  public Option<List<String>> getOwnerBorrowerIds() {
    return Option.of(ownerBorrowerIds);
  }

  public Option<TestAddressData> getAddress() {
    return Option.of(address);
  }

  public Option<TestPropertyType> getPropertyType() {
    return Option.of(propertyType);
  }

  public Option<TestPropertyUsageType> getCurrentUsage() {
    return Option.of(currentUsage);
  }

  public Option<Money> getEstimatedValue() {
    return Option.of(estimatedValue);
  }

  public Option<Integer> getNumberOfUnits() {
    return Option.of(numberOfUnits);
  }

  public Option<TestOwnedPropertyDisposition> getDisposition() {
    return Option.of(disposition);
  }

  public Option<TestRentalIncomeType> getRentalIncomeType() {
    return Option.of(rentalIncomeType);
  }

  public Option<List<String>> getIncomeIds() {
    return Option.of(incomeIds);
  }

  public Option<Money> getMonthlyInsurancePremiums() {
    return Option.of(monthlyInsurancePremiums);
  }

  public Option<Money> getMonthlyPropertyTaxes() {
    return Option.of(monthlyPropertyTaxes);
  }

  public Option<Money> getMonthlyHoaDues() {
    return Option.of(monthlyHoaDues);
  }

  public Option<List<String>> getLiabilityIds() {
    return Option.of(liabilityIds);
  }

  public Option<Boolean> getOwnershipSharedWithNonBorrower() {
    return Option.of(ownershipSharedWithNonBorrower);
  }

  @Override
  public TestMortgageDataId getInternalId() {
    return id != null ? TestMortgageDataId.of(id) : null;
  }

  @Override
  public void setInternalId(TestMortgageDataId id) {
    this.id = id != null ? id : null;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

}