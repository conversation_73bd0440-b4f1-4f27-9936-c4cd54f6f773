package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = {ExposeTo.API_SERVER}, namespace = ExposeType.RewriteNamespace.SERVICE)
enum TestPropertyCounty {
  ARAPAHOE {
    @Override
    public <T> T visit(PropertyCountyVisitor<T> visitor) {
      return visitor.visitArapahoe();
    }
  },
  DENVER {
    @Override
    public <T> T visit(PropertyCountyVisitor<T> visitor) {
      return visitor.visitDenver();
    }
  };

  public abstract <T> T visit(PropertyCountyVisitor<T> visitor);

  public interface PropertyCountyVisitor<T> {

    T visitArapahoe();

    T visitDenver();

  }
}