package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = { ExposeTo.API_SERVER }, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestBankruptcyChapterType {
  CHAPTER_SEVEN("Chapter 7"),
  CHAPTER_ELEVEN("Chapter 11"),
  CHAPTER_TWELVE("Chapter 12"),
  CHAPTER_THIRTEEN("Chapter 13");

  private final String label;

  TestBankruptcyChapterType(String label) {
    this.label = label;
  }

  public String getLabel() {
    return label;
  }

}
