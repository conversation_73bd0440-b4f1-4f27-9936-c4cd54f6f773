package com.kaching.mq;

import static com.kaching.platform.common.logging.Log.getLog;

import java.io.IOException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import org.weakref.jmx.Managed;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.name.Named;
import com.kaching.mq.rabbitmq.RabbitContext;
import com.kaching.mq.rabbitmq.RabbitContext.ChannelConsumer;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.util.Resettable;
import com.rabbitmq.client.AlreadyClosedException;

/**
 * Manages multiple {@link QueryConsumer} instances that invoke queries.
 */
public class QueryConsumerEngine {

  private static final Log log = getLog(QueryConsumerEngine.class);
  final AtomicInteger driverCount = new AtomicInteger(0);

  @Inject Provider<QueryConsumer> consumers;
  @Inject @Consumer ThreadPoolExecutor executor;
  @Inject StackTraceMonitor monitor;
  @Inject Resettable<RabbitContext> rabbitContext;
  @Inject @Named("shutting-down") AtomicBoolean shuttingDown;

  public void start() {
    for (int i = 0; i < executor.getMaximumPoolSize(); i++) {
      executor.submit(consumerTask());
    }
  }

  @VisibleForTesting
  Runnable consumerTask() {
    return () -> {
      log.info("Starting query consumer");
      driverCount.incrementAndGet();
      try {
        QueryConsumer consumer = consumers.get();
        while (!Thread.interrupted()) {
          try {
            if (shuttingDown.get()) {
              try {
                ChannelConsumer channelConsumer = rabbitContext.get().getConsumer().get();
                channelConsumer.getChannel().basicCancel(channelConsumer.getConsumer().getConsumerTag());
              } catch (AlreadyClosedException | IOException e) {
                if (shuttingDown.get()) {
                  Thread.currentThread().interrupt();
                } else {
                  throw e;
                }
              }
            }

            consumer.call();
          } catch (Exception e) {
            monitor.add(e);
            log.error(e, "Exception while consuming");
          }
        }
      } finally {
        log.warn("Query consumer thread was interrupted and will not be restarted!");
        driverCount.decrementAndGet();
      }
    };
  }

  @Managed
  public int getRunningDriverCount() {
    return driverCount.get();
  }
}
