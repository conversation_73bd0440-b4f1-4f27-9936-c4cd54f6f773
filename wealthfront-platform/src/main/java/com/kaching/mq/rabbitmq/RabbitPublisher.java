package com.kaching.mq.rabbitmq;

import static com.kaching.platform.common.logging.Log.getLog;
import static java.lang.String.format;
import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.IOException;
import java.net.SocketException;
import java.util.Set;

import com.google.inject.Inject;
import com.kaching.mq.Producer;
import com.kaching.mq.QueryInvocation;
import com.kaching.mq.QueryPublisher;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.util.Resettable;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.ShutdownSignalException;

/**
 * Publishes a {@link QueryInvocation} to the messaging system.
 * <p/>
 * TODO: Parameterize by {@link Query} to turn runtime binding error into Guice binding error.
 */
public class RabbitPublisher implements QueryPublisher {

  private static final Log log = getLog(RabbitPublisher.class);

  @Inject Resettable<RabbitContext> mq;
  @Inject @Producer Set<ChannelBinding> producerBindings;
  @Inject StackTraceMonitor monitor;

  @Override
  public void publish(QueryInvocation<?> invocation) throws IOException {
    publish(QueryChannelBinding.of(invocation.getQueryClass()), invocation);
  }

  @Override
  public void publishWithPriority(QueryInvocation<?> invocation, int priority) throws IOException {
    publish(QueryChannelBinding.of(invocation.getQueryClass()), invocation, priority);
  }

  @Override
  public void publish(ChannelBinding binding, QueryInvocation<?> invocation) throws IOException {
    publish(binding, invocation, null);
  }

  private void publish(ChannelBinding binding, QueryInvocation<?> invocation, Integer priority) throws IOException {
    if (!producerBindings.contains(binding)) {
      RuntimeException re = new RuntimeException(
          format("Skipping publishing query invocation %s because ChannelBinding is not bound as a producer",
              invocation));
      log.warn(re);
      monitor.add(re);
      return;
    }

    ShutdownSignalException shutdownSignalException = null;
    int tries = 0;
    while (tries < 3) {
      try {
        Channel channel = mq.get().getProducerChannel();
        synchronized (channel) {
          String exchange = binding.getExchangeName();
          String routingKey = binding.getRoutingKey();
          log.debug("Publishing to exchange %s, routing key %s, entity %s", exchange, routingKey, invocation);
          channel.basicPublish(
              exchange,
              routingKey,
              propertiesWithPriority(priority),
              QueryInvocation.MARSHALLER.marshall(invocation).toString().getBytes(UTF_8));
        }
        return; // <-- don't redo send
      } catch (ShutdownSignalException | SocketException e) {
        if (e instanceof ShutdownSignalException) {
          shutdownSignalException = (ShutdownSignalException) e;
        }
        log.warn(e, "RabbitMQ connection shutdown, re-connecting...");
        mq.reset();
        tries++;
      }
    }
    throw new IllegalStateException(format(
        "Couldn't publish %s after %s tries due to mq connection problem",
        invocation, tries + 1), shutdownSignalException);
  }

  private static AMQP.BasicProperties propertiesWithPriority(Integer priority) {
    return new AMQP.BasicProperties(null, null, null, 2,
        priority, null, null, null,
        null, null, null, null,
        null, null);
  }

}
