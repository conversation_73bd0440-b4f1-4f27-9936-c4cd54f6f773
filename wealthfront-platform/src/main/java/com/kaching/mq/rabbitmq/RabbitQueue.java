package com.kaching.mq.rabbitmq;

import static com.kaching.platform.common.logging.Log.getLog;

import java.io.IOException;

import com.google.inject.Inject;
import com.kaching.mq.MessageQueue;
import com.kaching.platform.common.logging.Log;
import com.kaching.util.Resettable;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.QueueingConsumer;
import com.rabbitmq.client.QueueingConsumer.Delivery;
import com.rabbitmq.client.ShutdownSignalException;

class RabbitQueue extends MessageQueue {

  private static final Log log = getLog(RabbitQueue.class);

  private static final int TIMEOUT_MILLIS = 1000;

  @Inject Resettable<RabbitContext> mq;

  @Override
  public MessageQueue.Message take() throws InterruptedException {
    try {
      QueueingConsumer consumer = mq.get().getConsumer().get().getConsumer();
      log.debug("Consumer is %s", consumer);
      Delivery delivery = consumer.nextDelivery(TIMEOUT_MILLIS);
      return delivery == null ? null : new RabbitMessage(delivery);
    } catch (ShutdownSignalException e) {
      log.warn(e, "RabbitMQ connection shutdown, re-connecting...");
      mq.reset();
      return null;
    }
  }

  class RabbitMessage implements MessageQueue.Message {

    private final Delivery delivery;

    private RabbitMessage(Delivery delivery) {
      this.delivery = delivery;
    }

    @Override
    public void ack() {
      log.debug("acking delivery %s in exchange %s, routing key %s",
          delivery.getEnvelope().getDeliveryTag(),
          delivery.getEnvelope().getExchange(),
          delivery.getEnvelope().getRoutingKey());
      Channel channel = mq.get().getConsumer().get().getChannel();
      synchronized (channel) {
        try {
          channel.basicAck(delivery.getEnvelope().getDeliveryTag(), false);
        } catch (IOException e) {
          log.warn(e, "Couldn't ack delivery %s", delivery);
        }
      }
    }

    @Override
    public byte[] getBytes() {
      return delivery.getBody();
    }

    @Override
    public void nack() {
      log.debug("nacking delivery %s in exchange %s, routing key %s",
          delivery.getEnvelope().getDeliveryTag(),
          delivery.getEnvelope().getExchange(),
          delivery.getEnvelope().getRoutingKey());
      mq.get().getConsumer().reset();
    }

    @Override
    public boolean isRedelivery() {
      return delivery.getEnvelope().isRedeliver();
    }
  }

}
