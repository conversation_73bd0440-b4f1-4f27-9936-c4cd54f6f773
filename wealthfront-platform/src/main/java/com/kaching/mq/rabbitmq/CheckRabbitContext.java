package com.kaching.mq.rabbitmq;

import static com.google.common.base.Preconditions.checkNotNull;
import static com.kaching.platform.monitoring.icinga.IcingaMetadata.Interval.ONE_MINUTE;
import static com.kaching.platform.monitoring.icinga.IcingaMetadata.TimePeriod.TWENTY_FOUR_X_SEVEN;
import static com.kaching.platform.monitoring.icinga.IcingaMetadata.icingaMetadataBuilder;

import com.google.inject.Inject;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.monitoring.icinga.IcingaCheck;
import com.kaching.platform.monitoring.icinga.IcingaMetadata;
import com.kaching.platform.monitoring.icinga.IcingaOutput;
import com.kaching.util.Resettable;
import com.kaching.util.pagerduty.ServiceKindToPagerDuty;

public class CheckRabbitContext implements IcingaCheck {

  @Inject ServiceKind serviceKind;
  @Inject Resettable<RabbitContext> rabbitContextProvider;

  @Override
  public IcingaMetadata getIcingaMetadata() {
    return icingaMetadataBuilder(this.getClass())
        .withInterval(ONE_MINUTE)
        .withTimePeriod(TWENTY_FOUR_X_SEVEN)
        .withIcingaGroup(ServiceKindToPagerDuty.getPagerDutyDevices(serviceKind).get(0))
        .build();
  }

  @Override
  public IcingaOutput getIcingaOutput() {
    try {
      RabbitContext rabbitContext = rabbitContextProvider.get();
      checkNotNull(rabbitContext);
      return IcingaOutput.OK;
    } catch (Exception e) {
      return IcingaOutput.icingaOutputBuilder()
          .withExitCode(IcingaOutput.ExitCode.CRITICAL)
          .withMessage("Error creating RabbitContext: ", e)
          .build();
    }
  }

}
