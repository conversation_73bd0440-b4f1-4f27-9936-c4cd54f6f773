package com.kaching.mq.rabbitmq;

import static java.lang.String.format;

import java.util.Map;

public abstract class ChannelBinding {

  private String exchangeName;
  private String exchangeType;
  private String queueName;
  private String routingKey;
  private final boolean isDurable = false;
  private final Map<String, Object> queueArgs;

  public ChannelBinding(String exchangeName, String exchangeType, String queueName, String routingKey, Map<String, Object> queueArgs) {
    this.exchangeName = exchangeName;
    this.exchangeType = exchangeType;
    this.queueName = queueName;
    this.routingKey = routingKey;
    this.queueArgs = queueArgs;
  }

  public String getExchangeName() {
    return exchangeName;
  }

  public String getExchangeType() {
    return exchangeType;
  }

  public String getQueueName() {
    return queueName;
  }

  public String getRoutingKey() {
    return routingKey;
  }

  public boolean isDurable() {
    return isDurable;
  }

  public Map<String, Object> getQueueArgs() {
    return queueArgs;
  }

  /**
   * @deprecated Set with constructor
   */
  @Deprecated
  void setExchangeName(String exchangeName) {
    this.exchangeName = exchangeName;
  }

  /**
   * @deprecated Set with constructor
   */
  @Deprecated
  void setExchangeType(String exchangeType) {
    this.exchangeType = exchangeType;
  }

  /**
   * @deprecated Set with constructor
   */
  @Deprecated
  void setQueueName(String queueName) {
    this.queueName = queueName;
  }

  /**
   * @deprecated Set with constructor
   */
  @Deprecated
  void setRoutingKey(String routingKey) {
    this.routingKey = routingKey;
  }

  @Override
  public String toString() {
    return format("%s[exchange=%s,type=%s,queue=%s,routingKey=%s,durable=%s,queueArgs=%s]",
        getClass().getSimpleName(), exchangeName, exchangeType, queueName, routingKey, isDurable, queueArgs);
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result
        + ((exchangeName == null) ? 0 : exchangeName.hashCode());
    result = prime * result
        + ((exchangeType == null) ? 0 : exchangeType.hashCode());
    result = prime * result + ((queueName == null) ? 0 : queueName.hashCode());
    result = prime * result
        + ((routingKey == null) ? 0 : routingKey.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) {
      return true;
    }
    if (obj == null) {
      return false;
    }
    if (getClass() != obj.getClass()) {
      return false;
    }
    ChannelBinding other = (ChannelBinding) obj;
    if (exchangeName == null) {
      if (other.exchangeName != null) {
        return false;
      }
    } else if (!exchangeName.equals(other.exchangeName)) {
      return false;
    }
    if (exchangeType == null) {
      if (other.exchangeType != null) {
        return false;
      }
    } else if (!exchangeType.equals(other.exchangeType)) {
      return false;
    }
    if (queueName == null) {
      if (other.queueName != null) {
        return false;
      }
    } else if (!queueName.equals(other.queueName)) {
      return false;
    }
    if (routingKey == null) {
      if (other.routingKey != null) {
        return false;
      }
    } else if (!routingKey.equals(other.routingKey)) {
      return false;
    }
    return true;
  }

}
