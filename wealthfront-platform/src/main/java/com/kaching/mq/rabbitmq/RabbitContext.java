package com.kaching.mq.rabbitmq;

import static com.kaching.platform.common.logging.Log.getLog;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicInteger;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.name.Named;
import com.kaching.mq.Consumer;
import com.kaching.mq.Producer;
import com.kaching.platform.common.logging.Log;
import com.kaching.util.Resettable;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.QueueingConsumer;

/**
 * Creates one {@link Connection} for consumers, one {@link Connection} for
 * producers.
 * <p/>
 * Producers get one {@link Channel}.
 * <p/>
 * Each consumer gets a thread-local {@link Channel} and {@link QueueingConsumer}.
 */
public class RabbitContext {

  private static final Log log = getLog(RabbitContext.class);
  Channel producerChannel;
  ThreadLocal<Resettable<ChannelConsumer>> consumers;

  /**
   * {@link Channel} and {@link QueueingConsumer} linked together
   * because we use QOS to limit the number of unacknowledged messages
   * the server sends into the consumer, and the only way to nack()
   * a message is to clean up the {@link Channel} and reinitialize.
   */
  public static class ChannelConsumer {

    private final Channel channel;
    private final QueueingConsumer consumer;
    private final Set<String> tags;

    public ChannelConsumer(Channel channel, QueueingConsumer consumer, Set<String> tags) {
      this.channel = channel;
      this.consumer = consumer;
      this.tags = tags;
    }

    public Channel getChannel() {
      return channel;
    }

    public QueueingConsumer getConsumer() {
      return consumer;
    }

    public Set<String> getTags() {
      return tags;
    }
  }

  @Inject
  RabbitContext(
      final Provider<Connection> connections,
      @Consumer final Set<ChannelBinding> consumerBindings,
      @Producer final Set<ChannelBinding> producerBindings,
      @Named("prefetch") AtomicInteger prefetch) throws IOException {
    final Connection connection = connections.get();
    this.consumers = new ThreadLocal<Resettable<ChannelConsumer>>() {
      @Override
      protected Resettable<ChannelConsumer> initialValue() {
        return newConsumer(connection, consumerBindings, prefetch.get());
      }
    };
    this.producerChannel = createChannel(connections.get(), producerBindings, prefetch.get());
  }

  Resettable<ChannelConsumer> newConsumer(
      final Connection connection,
      final Set<ChannelBinding> bindings,
      int prefetch) {
    return new Resettable<ChannelConsumer>() {
      @Override
      protected ChannelConsumer create() {
        return createChannelConsumer(connection, bindings, prefetch);
      }

      @Override
      protected void resetFailed(Exception e) {
        log.error(e, "Couldn't reset QueueingConsumer");
      }

      @Override
      protected void destroy(ChannelConsumer consumer) {
        destroyChannelConsumer(consumer);
      }
    };
  }

  @VisibleForTesting
  ChannelConsumer createChannelConsumer(
      final Connection connection, final Set<ChannelBinding> bindings, int prefetch) {
    log.debug("Creating ChannelConsumer with bindings %s", bindings);

    Channel channel;
    try {
      channel = createChannel(connection, bindings, prefetch);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }

    QueueingConsumer consumer = new QueueingConsumer(channel, new LinkedBlockingQueue<>(100));
    Set<String> tags = new HashSet<>();
    synchronized (channel) {
      for (ChannelBinding binding : bindings) {
        try {
          String tag = channel.basicConsume(binding.getQueueName(), consumer);
          log.debug("Bound consumer %s queue %s to tag %s",
              consumer,
              binding.getQueueName(),
              tag);
          tags.add(tag);
        } catch (IOException e) {
          throw new RuntimeException(e);
        }
      }
    }
    log.debug("Channel %s, consumer %s created and bound to %s",
        channel,
        consumer,
        bindings);
    return new ChannelConsumer(channel, consumer, tags);
  }

  void destroyChannelConsumer(ChannelConsumer consumer) {
    log.debug("Cancelling channel consumer %s", consumer);
    Channel channel = consumer.getChannel();
    synchronized (channel) {
      for (String tag : consumer.getTags()) {
        try {
          log.debug("Cancelling consumer %s tag %s", consumer, tag);
          channel.basicCancel(tag);
        } catch (Exception e) {
          log.error(e);
        }
      }

      try {
        channel.close();
      } catch (Exception e) {
        log.error(e);
      }
    }
  }

  @VisibleForTesting
  Channel createChannel(
      Connection connection,
      Set<ChannelBinding> bindings,
      int prefetch) throws IOException {
    Channel channel = connection.createChannel();

    // One-to-one mapping of query type, exchange and queue.
    for (ChannelBinding binding : bindings) {
      channel.exchangeDeclare(binding.getExchangeName(), binding.getExchangeType());
      channel.queueDeclare(binding.getQueueName(), false, binding.isDurable(), false, false, binding.getQueueArgs());
      channel.queueBind(binding.getQueueName(), binding.getExchangeName(), binding.getRoutingKey());

      log.info("Binding exchange %s to queue %s with routing key %s",
          binding.getExchangeName(),
          binding.getQueueName(),
          binding.getRoutingKey());
    }

    // Server will push max(N) un-acknowledged deliveries.
    // TODO: Runtime property for QOS (queue-length) size.
    channel.basicQos(prefetch);

    return channel;
  }

  public Resettable<ChannelConsumer> getConsumer() {
    return consumers.get();
  }

  public Channel getProducerChannel() {
    return producerChannel;
  }

}
