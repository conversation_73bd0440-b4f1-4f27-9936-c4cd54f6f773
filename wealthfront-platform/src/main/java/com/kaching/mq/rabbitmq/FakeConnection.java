package com.kaching.mq.rabbitmq;

import java.io.IOException;

import com.rabbitmq.client.Address;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionParameters;
import com.rabbitmq.client.ShutdownListener;
import com.rabbitmq.client.ShutdownSignalException;

final class FakeConnection implements Connection {

  @Override
  public void removeShutdownListener(ShutdownListener listener) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void notifyListeners() {
    throw new UnsupportedOperationException();
  }

  @Override
  public boolean isOpen() {
    throw new UnsupportedOperationException();
  }

  @Override
  public ShutdownSignalException getCloseReason() {
    throw new UnsupportedOperationException();
  }

  @Override
  public void addShutdownListener(ShutdownListener listener) {
    throw new UnsupportedOperationException();
  }

  @Override
  public int getPort() {
    throw new UnsupportedOperationException();
  }

  @Override
  public ConnectionParameters getParameters() {
    throw new UnsupportedOperationException();
  }

  @Override
  public Address[] getKnownHosts() {
    throw new UnsupportedOperationException();
  }

  @Override
  public String getHost() {
    throw new UnsupportedOperationException();
  }

  @Override
  public int getHeartbeat() {
    throw new UnsupportedOperationException();
  }

  @Override
  public int getFrameMax() {
    throw new UnsupportedOperationException();
  }

  @Override
  public int getChannelMax() {
    throw new UnsupportedOperationException();
  }

  @Override
  public Channel createChannel(int channelNumber) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public Channel createChannel() throws IOException {
    return new FakeChannel();
  }

  @Override
  public void close(int closeCode, String closeMessage, int timeout)
      throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void close(int closeCode, String closeMessage) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void close(int timeout) throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void close() throws IOException {
    throw new UnsupportedOperationException();
  }

  @Override
  public void abort(int closeCode, String closeMessage, int timeout) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void abort(int closeCode, String closeMessage) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void abort(int timeout) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void abort() {
    throw new UnsupportedOperationException();
  }
}
