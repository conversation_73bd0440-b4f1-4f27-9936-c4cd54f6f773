package com.kaching.mq;

import org.kohsuke.args4j.Option;

import com.google.inject.AbstractModule;
import com.google.inject.Inject;
import com.kaching.mq.events.EventPublisherModule;
import com.kaching.mq.rabbitmq.RabbitMQModule;
import com.kaching.mq.rabbitmq.RabbitMQModule.Builder;
import com.kaching.platform.components.Component;

@Component(
    cmdOptions = MqComponent.Options.class,
    modules = MqComponent.Module.class
)
public class MqComponent {

  static class Options {

    @Option(name = "--fake-mq", usage = "Fake message queue.")
    public boolean fakeMQ = false;

  }

  static class Module extends AbstractModule {

    private final Options options;

    @Inject
    Module(Options options) {
      this.options = options;
    }

    @Override
    protected void configure() {
      Builder mq = RabbitMQModule.builder();
      if (options.fakeMQ) {
        mq.asFake();
      }
      install(mq.build());
      install(new EventPublisherModule(options.fakeMQ));
    }

  }

}
