package com.kaching.mq;

import java.io.IOException;

import com.kaching.mq.rabbitmq.ChannelBinding;

public interface QueryPublisher {

  void publish(QueryInvocation<?> invocation) throws IOException;

  /**
   * See https://www.rabbitmq.com/priority.html for priority info. The queue must be declared with x-max-priority
   * argument for it to be a priority queue. Higher number = higher priority.
   */
  void publishWithPriority(QueryInvocation<?> invocation, int priority) throws IOException;

  default void publish(ChannelBinding channelBinding, QueryInvocation<?> invocation) throws IOException {
    publish(invocation);
  }

}
