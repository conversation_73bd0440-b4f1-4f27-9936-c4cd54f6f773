package com.kaching.mq;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.atomic.AtomicLong;

import org.weakref.jmx.Managed;

import com.google.inject.Inject;
import com.kaching.JsonMarshallerFactory;
import com.kaching.KachingInstantiators;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.QueryExecutorService;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.twolattes.json.Json;

/**
 * Consumes {@link QueryInvocation} messages from the messaging system and
 * invokes the associated {@link Query}.
 * <p/>
 * Each {@link Query} type is mapped one-to-one with a message queue named as
 * the class of the {@link Query}.
 */
class QueryConsumer implements Callable<Boolean> {

  private static final Log log = Log.getLog(QueryConsumer.class);
  final AtomicLong consumeCount = new AtomicLong(0);
  final AtomicLong failedQueryCount = new AtomicLong(0);

  @Inject QueryExecutorService executor;
  @Inject MessageQueue q;
  @Inject StackTraceMonitor monitor;
  @Inject JsonMarshallerFactory jsonMarshallerFactory;

  @Override
  public Boolean call() throws Exception {
    q.next(new QueryMessageExecutor());
    return true;
  }

  @Managed
  public long getConsumeCount() {
    return consumeCount.get();
  }

  @Managed
  public long getFailedQueryCount() {
    return failedQueryCount.get();
  }

  class QueryMessageExecutor implements MessageQueue.WithMessage {

    @Override
    public void withMessage(byte[] message, boolean isRedelivery) throws Exception {
      if (message == null) {
        return;
      }

      Query<?> query = null;
      try {
        query = getQuery(message);
      } catch (ClassNotFoundException e) {
        log.error(e, "Couldn't match query type to message");
        monitor.add(e);
        // Treat bad data as successful message processing.
        return;
      } catch (IOException | IllegalArgumentException e) {
        log.error(e, "Couldn't unmarshall message into query");
        monitor.add(e);
        // Treat bad data as successful message processing.
        return;
      } catch (InvalidArgumentException e) {
        log.error(e, "Couldn't process arguments of query");
        monitor.add(e);
        // Treat bad data as successful message processing.
        return;
      }

      try {
        Future<?> future = executor.submit(query);
        future.get();
      } catch (RejectedExecutionException | InterruptedException e) {
        // Always nack() this because the query couldn't be executed.
        throw e;
      } catch (ExecutionException e) {
        if (isRedelivery) {
          // Failed to execute after redelivery: ack() it and log error.
          log.error(e, "Couldn't execute query after being redelivered, won't execute it again");
        } else {
          // Redeliver.
          throw e;
        }
      }
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    Query<?> getQuery(byte[] data) throws ClassNotFoundException, IOException {
      QueryInvocation invocation =
          QueryInvocation.MARSHALLER.unmarshall(Json.read(new InputStreamReader(new ByteArrayInputStream(data))));
      // TODO: The best way to get the class?
      Class<? extends Query<?>> queryClass = (Class<? extends Query<?>>) Class.forName(invocation.getQueryClass());
      return KachingInstantiators.createInstantiator(queryClass, jsonMarshallerFactory)
          .newInstance(invocation.parameters);
    }

  }

}
