package com.kaching.mq;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.ElementType.PARAMETER;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.google.inject.BindingAnnotation;

@Target({PARAMETER, FIELD, METHOD})
@Retention(RetentionPolicy.RUNTIME)
@BindingAnnotation
public @interface Consumer {

}
