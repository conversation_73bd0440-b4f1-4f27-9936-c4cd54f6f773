package com.kaching.mq;

import java.util.concurrent.atomic.AtomicBoolean;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.kaching.platform.common.logging.Log;

public abstract class MessageQueue {

  private static final Log log = Log.getLog(MessageQueue.class);

  @Inject @Named("shutting-down") AtomicBoolean shuttingDown;

  public interface Message {

    byte[] getBytes();

    boolean isRedelivery();

    void ack();

    void nack();
  }

  public interface WithMessage {

    void withMessage(byte[] message, boolean isRedelivery) throws Exception;
  }

  void next(WithMessage with) {
    Message message = null;
    try {
      message = take();
      if (message != null) {
        with.withMessage(message.getBytes(), message.isRedelivery());
        message.ack();
      } else if (shuttingDown.get()) {
        Thread.currentThread().interrupt(); //interrupts to stop the task in QueryConsumerEngine when out of items in the queue
      }
    } catch (Exception e) {
      log.error(e, "Exception taking or handling message");
      if (message != null) {
        message.nack();
      }
    }
  }

  public abstract Message take() throws InterruptedException;

}
