package com.kaching.mq;

import java.util.concurrent.atomic.AtomicBoolean;

import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.queryengine.AbstractQuery;

public class ShutdownMessageQueue extends AbstractQuery<Unit> {

  @Inject(optional = true) @Named("shutting-down") AtomicBoolean shuttingDown;

  @Override
  public Unit process() {
    if (shuttingDown != null) {
      shuttingDown.set(true);
    }
    return Unit.unit;
  }

}
