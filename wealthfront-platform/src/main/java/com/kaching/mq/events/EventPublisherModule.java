package com.kaching.mq.events;

import static com.google.common.collect.Iterables.toArray;
import static com.google.common.collect.Iterables.transform;
import static com.kaching.platform.queryengine.QueryEngine.QuerySchedule.newQuerySchedule;
import static com.kaching.platform.queryengine.QueryEngineModule.registerQueries;
import static com.kaching.platform.queryengine.QueryEngineModule.registerQuerySchedules;
import static com.kaching.util.quartz.TriggerFactory.CronBuilder.everyDay;

import java.io.IOException;

import com.google.common.base.Function;
import com.google.common.base.Splitter;
import com.google.inject.AbstractModule;
import com.google.inject.Provider;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.google.inject.name.Names;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.guice.ApplicationOptions;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.util.Resettable;
import com.kaching.util.quartz.TriggerFactory;
import com.rabbitmq.client.Address;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.ConnectionParameters;
import com.wealthfront.data.ingestion.kafka.IngestionKafkaModule;
import com.wealthfront.data.ingestion.kafka.events.KafkaEventPublisher;
import com.wealthfront.data.ingestion.kafka.events.RabbitAndKafkaEventPublisher;

public class EventPublisherModule extends AbstractModule {

  private static final Log log = Log.getLog(EventPublisherModule.class);
  private final boolean fakeMq;

  // TODO remove constructor when fully migrated to components
  public EventPublisherModule(ApplicationOptions options) {
    this(options.fakeMQ);
  }

  public EventPublisherModule(boolean fakeMq) {
    this.fakeMq = fakeMq;
  }

  @SuppressWarnings("unchecked")
  @Override
  protected void configure() {
    if (fakeMq) {
      bind(EventPublisher.class).to(EmptyEventPublisher.class);
      bind(EventPublisher.class)
          .annotatedWith(Names.named("kafka"))
          .to(EmptyEventPublisher.class);
      bind(EventPublisher.class)
          .annotatedWith(Names.named("rabbit_and_kafka"))
          .to(EmptyEventPublisher.class);
    } else {
      install(new IngestionKafkaModule());
      bind(EventPublisher.class)
          .annotatedWith(Names.named("kafka"))
          .to(KafkaEventPublisher.class);
      bind(EventPublisher.class)
          .annotatedWith(Names.named("rabbit_and_kafka"))
          .to(RabbitAndKafkaEventPublisher.class);
      TriggerFactory triggerFactory = new TriggerFactory();
      registerQuerySchedules(binder(),
          //newQuerySchedule(PublishVarz2.class, triggerFactory.cron(everyDay().everyNMinutes(5))),
          newQuerySchedule(PublishHeartbeat.class, triggerFactory.cron(everyDay().everyNMinutes(1))));
      registerQueries(binder(), PublishHeartbeat.class);
    }
  }

  @Provides
  @Events
  Connection connection(@Named("rabbitmq.hosts") String hosts, ConnectionParameters params) throws IOException {
    try {
      log.info("Connecting to RabbitMQ on hosts %s", hosts);
      Connection connection =
          new ConnectionFactory(params).newConnection(toArray(transform(
              Splitter.on(',').split(hosts),
              new Function<String, Address>() {
                @Override
                public Address apply(String from) {
                  return new Address(from);
                }
              }), Address.class));
      log.info("Connected to RabbitMQ on hosts %s", hosts);
      return connection;
    } catch (IOException e) {
      log.error(e, "Failed to connect to RabbitMQ on hosts %s", hosts);
      throw e;
    }
  }

  @Provides
  @Singleton
  Resettable<RabbitEventContext> context(final Provider<RabbitEventContext> delegate, StackTraceMonitor stm) {
    return new Resettable<RabbitEventContext>() {
      @Override
      protected RabbitEventContext create() {
        return delegate.get();
      }

      @Override
      protected void resetFailed(Exception e) {
        log.error(e, "Error creating RabbitContext");
        stm.add(e);
      }

      @Override
      protected int numRetries() {
        return 20;
      }
    };
  }

}
