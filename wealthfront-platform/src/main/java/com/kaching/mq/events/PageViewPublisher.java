package com.kaching.mq.events;

import static java.util.concurrent.TimeUnit.SECONDS;

import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

import org.joda.time.DateTime;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.util.concurrent.RateLimiter;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.kaching.mq.events.Event.ModelRecommendationsEvent;
import com.kaching.mq.events.Event.PageViewEvent;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.Manifest;
import com.kaching.platform.discovery.ServiceAllocation;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.queryengine.StackTraceMonitor;

@Singleton
public class PageViewPublisher implements Runnable {

  private static final Log log = Log.getLog(PageViewPublisher.class);
  private static final double ONCE_PER_SIX_SECONDS = 1.0 / 6.0;
  @VisibleForTesting static int QUEUE_CAPACITY = 2500;
  private final LinkedBlockingQueue<Event> queue = new LinkedBlockingQueue<>(QUEUE_CAPACITY);
  @VisibleForTesting
  RateLimiter rateLimiter = RateLimiter.create(ONCE_PER_SIX_SECONDS);

  @Inject(optional = true) @Named("rabbit_and_kafka") EventPublisher publisher;
  @Inject Provider<DateTime> clock;
  @Inject(optional = true) ServiceDescriptor descriptor;
  @Inject(optional = true) Manifest manifest;
  @Inject(optional = true) StackTraceMonitor monitor;

  @Deprecated
  public boolean publish(
      String method, String host, String path,
      String remoteHost, String userAgent,
      String referrer, double elapsed,
      Long userId, int statusCode, String token, List<String> experiments) {
    return publish(
        method, host, path, remoteHost, userAgent, referrer,
        elapsed, userId, statusCode, token, experiments, null, null);
  }

  @Deprecated
  public boolean publish(
      String method, String host, String path,
      String remoteHost, String userAgent,
      String referrer, double elapsed,
      Long userId, int statusCode, String token,
      List<String> experiments, String pageViewId) {
    return publish(method, host, path, remoteHost, userAgent, referrer, elapsed,
        userId, statusCode, token, experiments, pageViewId, null);
  }

  public boolean publish(
      String method, String host, String path,
      String remoteHost, String userAgent,
      String referrer, double elapsed,
      Long userId, int statusCode, String token,
      List<String> experiments, String pageViewId, String query) {
    for (ServiceAllocation allocation : getAllocation()) {
      PageViewEvent event = PageViewEvent.eventOf(
          clock.get(), descriptor.getKind(), allocation.getMachineHost(),
          method, host, path, remoteHost, userAgent, referrer, elapsed, userId, statusCode,
          token, experiments, pageViewId, query);
      return publishEvent(event);
    }
    return false;
  }

  private Option<ServiceAllocation> getAllocation() {
    try {
      if (descriptor == null) {
        return Option.none();
      }
      return Option.of(manifest.getAllocation(descriptor.getId()));
    } catch (Exception e) {
      return Option.none();
    }
  }

  @Deprecated
  public boolean publishRecommendations(
      String trackingCookie, String questionnaireJson, List<Long> recommendations) {
    return publishRecommendations(null, trackingCookie, questionnaireJson, recommendations);
  }

  public boolean publishRecommendations(
      String requestId, String trackingCookie, String questionnaireJson, List<Long> recommendations) {
    for (ServiceAllocation allocation : getAllocation()) {
      ModelRecommendationsEvent mre = new ModelRecommendationsEvent(
          clock.get(), descriptor.getKind(), allocation.getMachineHost(),
          trackingCookie, requestId, questionnaireJson, recommendations);
      return publishEvent(mre);
    }
    return false;
  }

  public boolean publishUserEvent(
      String pageViewId, String trackingCookie, String remoteHost,
      Long userId, String name, String payload) {
    return publishUserEvent(pageViewId, trackingCookie, remoteHost, userId, "", name, payload);
  }

  public boolean publishUserEvent(
      String pageViewId, String trackingCookie, String remoteHost,
      Long userId, String category, String name, String payload) {
    for (ServiceAllocation allocation : getAllocation()) {
      return publishEvent(new Event.UserEvent(
          clock.get(), descriptor.getKind(), allocation.getMachineHost(),
          pageViewId, trackingCookie,
          remoteHost, userId, category, name, payload));
    }
    return false;
  }

  public boolean publishQueryInvocationEvent(
      String queryName, String queryHost, Integer elapsed, Integer status, String queryArguments,
      Long userId, String pageViewId, boolean isTimeout) {
    for (ServiceAllocation allocation : getAllocation()) {
      return publishEvent(new Event.QueryInvocationEvent(
          clock.get(), descriptor.getKind(), allocation.getMachineHost(),
          queryName, queryHost, elapsed, status, queryArguments, userId, pageViewId, isTimeout));
    }
    return false;
  }

  public boolean publishSentryErrorEvent(String sentryData, String sentryClientIdentifier) {
    for (ServiceAllocation allocation : getAllocation()) {
      return publishEvent(
          new Event.SentryErrorEvent(clock.get(), descriptor.getKind(), allocation.getMachineHost(), sentryData,
              sentryClientIdentifier));
    }
    return false;
  }

  @VisibleForTesting
  boolean publishEvent(Event event) {
    if (publisher == null) {
      log.info("event publisher wasn't injected, skipping publishing");
      return false;
    }
    if (descriptor == null) {
      log.info("service descriptor wasn't injected, skipping publishing");
      return false;
    }
    if (manifest == null) {
      log.info("manifest wasn't injected, skipping publishing");
      return false;
    }

    try {
      queue.add(event);
      return true;
    } catch (Exception e) {
      log.info(e, "Unable to add page view event to queue");
      if (monitor != null && rateLimiter.tryAcquire()) {
        monitor.add(e);
      }
      return false;
    }
  }

  @Override
  public void run() {
    while (true) {
      if (!takeAndPublish()) {
        return;
      }
    }
  }

  @VisibleForTesting
  boolean takeAndPublish() {
    try {
      Event event = queue.poll(3, SECONDS);
      if (event == null) {
        return true;
      }
      publisher.publish(event);
    } catch (InterruptedException ie) {
      log.warn("PageViewPublisher was interrupted, exiting");
      return false;
    } catch (Exception e) {
      log.info(e, "Problem while polling and publishing a page view");
    }
    return true;
  }

}
