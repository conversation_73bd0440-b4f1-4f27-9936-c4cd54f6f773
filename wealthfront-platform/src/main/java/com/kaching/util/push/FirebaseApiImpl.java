package com.kaching.util.push;

import static com.google.common.base.Charsets.UTF_8;
import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.kaching.util.http.ResultBasedHttpClient.Builder;
import static com.kaching.util.http.ResultBasedHttpClient.Response;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Inject;
import com.kaching.platform.timing.Chronograph;
import com.kaching.util.functional.Result;
import com.kaching.util.http.ResultBasedHttpClient;

public class FirebaseApiImpl implements FirebaseApi {

  private static final String url = "https://fcm.googleapis.com/fcm/send";

  @Inject ResultBasedHttpClient httpClient;
  @Inject Chronograph chrono;

  @Override
  public Result<Response> send(String apiKey, FirebaseRequest request) {
    return executePost(getBuilder(apiKey, request), "fcm-send");
  }

  @Inject
  public void setHttpClient(ResultBasedHttpClient httpClient) {
    this.httpClient = httpClient;
  }

  @Inject
  public void setChrono(Chronograph chrono) {
    this.chrono = chrono;
  }

  @VisibleForTesting
  private Builder getBuilder(String apiKey, FirebaseRequest request) {
    String requestEntity = createEntityMarshaller(FirebaseRequest.class).marshall(request).toString();
    return httpClient.request(url)
        .header("Authorization", "key=" + apiKey)
        .entity(requestEntity, "application/json", UTF_8.name());
  }

  private Result<Response> executePost(Builder builder, String eventName) {
    return chrono.time(FirebaseApiImpl.class, eventName, builder::executePost);
  }

}
