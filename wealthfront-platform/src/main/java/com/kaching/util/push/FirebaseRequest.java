package com.kaching.util.push;

import static com.google.common.base.Preconditions.checkArgument;
import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;

import java.util.Map;

import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;

@Entity
public class FirebaseRequest {

  public static final Marshaller<FirebaseRequest> MARSHALLER = createEntityMarshaller(FirebaseRequest.class);

  // Targets
  @Value(optional = true) private String to;
  @Value(name = "registration_ids") private String[] registrationIds;
  @Value(optional = true) private String condition;

  // Options
  @Value(optional = true, name = "collapse_key") private String collapseKey;
  @Value(optional = true) private Priority priority;
  @Value(optional = true, name = "content_available") private Boolean contentAvailable;
  @Value(optional = true, name = "time_to_live") private Integer timeToLive;
  @Value(optional = true, name = "restricted_package_name") private String restrictedPackageName;
  @Value(optional = true, name = "dry_run") private Boolean dryRun;

  // Payload
  @Value(optional = true) private Map<String, String> data;
  @Value(optional = true) private Notification notification;

  private FirebaseRequest() {
    /* JSON */
  }

  public FirebaseRequest(String... registrationIds) {
    checkArgument(registrationIds.length > 0);
    this.registrationIds = registrationIds;
  }

  public String getTo() {
    return to;
  }

  public void setTo(String to) {
    this.to = to;
  }

  public Boolean getContentAvailable() {
    return contentAvailable;
  }

  public void setContentAvailable(Boolean contentAvailable) {
    this.contentAvailable = contentAvailable;
  }

  public Boolean getDryRun() {
    return dryRun;
  }

  public void setDryRun(Boolean dryRun) {
    this.dryRun = dryRun;
  }

  public Integer getTimeToLive() {
    return timeToLive;
  }

  public void setTimeToLive(Integer timeToLive) {
    this.timeToLive = timeToLive;
  }

  public Map<String, String> getData() {
    return data;
  }

  public void setData(Map<String, String> data) {
    this.data = data;
  }

  public Notification getNotification() {
    return notification;
  }

  public void setNotification(Notification notification) {
    this.notification = notification;
  }

  public String getCollapseKey() {
    return collapseKey;
  }

  public void setCollapseKey(String collapseKey) {
    this.collapseKey = collapseKey;
  }

  public String getCondition() {
    return condition;
  }

  public void setCondition(String condition) {
    this.condition = condition;
  }

  public Priority getPriority() {
    return priority;
  }

  public void setPriority(Priority priority) {
    this.priority = priority;
  }

  public String getRestrictedPackageName() {
    return restrictedPackageName;
  }

  public void setRestrictedPackageName(String restrictedPackageName) {
    this.restrictedPackageName = restrictedPackageName;
  }

  public String[] getRegistrationIds() {
    return registrationIds;
  }

  public void setRegistrationIds(String[] registrationIds) {
    this.registrationIds = registrationIds;
  }

  public enum Priority {
    normal,
    high
  }

  @Entity
  public static class Notification {

    @Value(optional = true) private String title;
    @Value(optional = true) private String body;
    @Value(optional = true) private String sound;
    @Value(optional = true, name = "click_action") private String clickAction;
    @Value(optional = true, name = "body_loc_key") private String bodyLocKey;
    @Value(optional = true, name = "body_loc_args") private String[] bodyLocArgs;
    @Value(optional = true, name = "title_loc_key") private String titleLocKey;
    @Value(optional = true, name = "title_loc_args") private String[] titleLocArgs;
    @Value(optional = true, name = "android_channel_id") private String androidChannelId;

    public String getBody() {
      return body;
    }

    public void setBody(String body) {
      this.body = body;
    }

    public String getBodyLocKey() {
      return bodyLocKey;
    }

    public void setBodyLocKey(String bodyLocKey) {
      this.bodyLocKey = bodyLocKey;
    }

    public String getClickAction() {
      return clickAction;
    }

    public void setClickAction(String clickAction) {
      this.clickAction = clickAction;
    }

    public String getSound() {
      return sound;
    }

    public void setSound(String sound) {
      this.sound = sound;
    }

    public String getTitle() {
      return title;
    }

    public void setTitle(String title) {
      this.title = title;
    }

    public String getTitleLocKey() {
      return titleLocKey;
    }

    public void setTitleLocKey(String titleLocKey) {
      this.titleLocKey = titleLocKey;
    }

    public String[] getBodyLocArgs() {
      return bodyLocArgs;
    }

    public void setBodyLocArgs(String[] bodyLocArgs) {
      this.bodyLocArgs = bodyLocArgs;
    }

    public String[] getTitleLocArgs() {
      return titleLocArgs;
    }

    public void setTitleLocArgs(String[] titleLocArgs) {
      this.titleLocArgs = titleLocArgs;
    }

    public void setAndroidChannelId(String androidChannelId) {
      this.androidChannelId = androidChannelId;
    }

    public Option<String> getAndroidChannelId() {
      return Option.of(androidChannelId);
    }

  }

}
