package com.kaching.util.time.calendar;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.queryengine.scheduler.AbstractScheduledJob;
import com.kaching.platform.queryengine.scheduler.JobStatus;

@VisibleForTesting
public class Nop<PERSON>ob extends AbstractScheduledJob {

  @Override
  public JobStatus process() {
    return JobStatus.SUCCEEDED;
  }

  @Override
  public boolean shouldProcess() {
    return false;
  }

}
