package com.kaching.util.time.calendar;

import java.util.HashSet;
import java.util.Set;

import org.joda.time.LocalDate;

import com.google.common.base.Preconditions;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.name.Names;
import com.kaching.platform.queryengine.AbstractQuery;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.util.time.calendar.Calendar;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;

public class GetCalendarForDateRange extends AbstractQuery<GetCalendarForDateRange.Result> {

  @Entity
  @ExposeType(value = { ExposeTo.BACKEND }, namespace = RewriteNamespace.DO_NOT_COPY)
  public static class Result {

    @Value
    String calendarName;

    @Value
    LocalDate beginDate;

    @Value
    LocalDate endDate;

    @Value
    Set<LocalDate> includedDates;

    Result() { /* json */ }

    public Result(
        String calendarName, LocalDate beginDate, LocalDate endDate, Set<LocalDate> includedDates) {
      this.calendarName = calendarName;
      this.beginDate = beginDate;
      this.endDate = endDate;
      this.includedDates = includedDates;
    }

    public String getCalendarName() {
      return calendarName;
    }

    public LocalDate getBeginDate() {
      return beginDate;
    }

    public LocalDate getEndDate() {
      return endDate;
    }

    public Set<LocalDate> getIncludedDates() {
      return includedDates;
    }

  }

  private final String calendarName;
  private final LocalDate beginDate;
  private final LocalDate endDate;

  public GetCalendarForDateRange(String calendarName, LocalDate beginDate, LocalDate endDate) {
    this.calendarName = calendarName;
    this.beginDate = beginDate;
    this.endDate = endDate;
  }

  @Inject Injector injector;

  @Override
  public Result process() {
    Preconditions.checkArgument(!beginDate.isAfter(endDate),
        "begin date %s can not be after end date %s", beginDate, endDate);

    Calendar calendar = injector.getInstance(Key.get(Calendar.class, Names.named(calendarName)));
    Set<LocalDate> includedDates = new HashSet<>();
    for (LocalDate date = beginDate; !date.isAfter(endDate); date = date.plusDays(1)) {
      if (calendar.includesDay(date).getOrThrow()) {
        includedDates.add(date);
      }
    }
    return new Result(calendarName, beginDate, endDate, includedDates);
  }

}
