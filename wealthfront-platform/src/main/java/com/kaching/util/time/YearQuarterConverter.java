package com.kaching.util.time;

import com.kaching.platform.common.Strings;
import com.kaching.platform.converters.NullHandlingConverter;

public class YearQuarterConverter extends NullHandlingConverter<YearQuarter> {

  @Override
  protected YearQuarter fromNonNullableString(String representation) {
    return YearQuarter.from(representation);
  }

  @Override
  protected String nonNullableToString(YearQuarter value) {
    return Strings.format("%s-%s", value.getYear(), value.getQuarter());
  }
}
