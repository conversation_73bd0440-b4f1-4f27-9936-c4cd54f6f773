package com.kaching.util.mail;

import static java.lang.Long.parseLong;

import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.converters.ConvertedBy;
import com.kaching.platform.converters.NullHandlingConverter;
import com.twolattes.json.Json;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.types.NullSafeType;

@ConvertedBy(QueuedPageId.Converter.class)
@MarshalledBy(QueuedPageId.JsonType.class)
public class QueuedPageId extends AbstractIdentifier<Long> {

  public QueuedPageId(long id) {
    super(id);
  }

  public static class Converter extends NullHandlingConverter<QueuedPageId> {

    @Override
    protected QueuedPageId fromNonNullableString(String representation) {
      return new QueuedPageId(parseLong(representation));
    }

    @Override
    protected String nonNullableToString(QueuedPageId value) {
      return value.toString();
    }

  }

  public static class JsonType extends NullSafeType<QueuedPageId, Json.Number> {

    @Override
    protected Json.Number nullSafeMarshall(QueuedPageId queuedPageId) {
      return Json.number(queuedPageId.getId());
    }

    @Override
    protected QueuedPageId nullSafeUnmarshall(Json.Number number) {
      return new QueuedPageId(number.getNumber().longValueExact());
    }

  }

}
