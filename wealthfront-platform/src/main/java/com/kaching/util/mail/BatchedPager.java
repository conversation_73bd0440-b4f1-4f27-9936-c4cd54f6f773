package com.kaching.util.mail;

import java.util.Collection;
import java.util.Set;

import org.joda.time.DateTime;
import org.joda.time.Duration;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Function;
import com.google.common.collect.LinkedListMultimap;
import com.google.common.collect.Multimap;
import com.google.common.collect.Multimaps;
import com.google.inject.Provider;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.util.functional.Tuple3;

public class BatchedPager implements Pager {

  private static final int STANDARD_BATCH_SIZE = 50;
  private static final Duration STANDARD_DURATION = Duration.standardMinutes(30);

  private final int batchSize;
  private final Duration duration;
  private final Pager pager;
  private final Provider<DateTime> clock;

  private final Multimap<String, PendingAlert> alertsBySubject = LinkedListMultimap.create();
  private boolean sentFirstAlertThisBatch = false;
  private DateTime lastAlertTime;

  public BatchedPager(
      Option<Integer> maybeBatchSize, Option<Duration> maybeDuration, Pager pager, Provider<DateTime> clock) {
    this.batchSize = maybeBatchSize.getOrElse(STANDARD_BATCH_SIZE);
    this.duration = maybeDuration.getOrElse(STANDARD_DURATION);
    this.pager = pager;
    this.clock = clock;
  }

  @Override
  public synchronized void alert(String subject, String message, Throwable throwable, Device device) {
    alert(subject, message, Option.some(throwable), device);
  }

  @Override
  public synchronized void alert(String subject, String message, Device device) {
    alert(subject, message, Option.none(), device);
  }

  @Override
  public void resolve(String subject, String message, Device device) {
    pager.resolve(subject, message, device);
  }

  @Override
  public void resolveIfOpen(String subject, String message, Device device) {
    pager.resolveIfOpen(subject, message, device);
  }

  @Override
  public void resolveIfOpen(Set<Tuple3<String, String, Device>> alerts) {
    pager.resolveIfOpen(alerts);
  }

  private synchronized void alert(String subject, String message, Option<Throwable> maybeThrowable, Device device) {
    if (!sentFirstAlertThisBatch) {
      if (maybeThrowable.isDefined()) {
        pager.alert(subject, message, maybeThrowable.getOrThrow(), device);
      } else {
        pager.alert(subject, message, device);
      }
      lastAlertTime = clock.get();
      sentFirstAlertThisBatch = true;
    } else {
      alertsBySubject.put(subject, new PendingAlert(subject, message, maybeThrowable, device));
      if (alertsBySubject.size() == batchSize) {
        pushCurrentAlerts();
      }
    }
  }

  public synchronized boolean hasPendingAlerts() {
    return !alertsBySubject.isEmpty();
  }

  public synchronized void pushCurrentAlerts() {
    for (String subject : alertsBySubject.keySet()) {
      Multimap<Device, PendingAlert> alertsByDevice = Multimaps.index(alertsBySubject.get(subject),
          new Function<PendingAlert, Device>() {
            @Override
            public Device apply(PendingAlert pendingAlert) {
              return pendingAlert.getDevice();
            }
          });
      for (Device device : alertsByDevice.keySet()) {
        String message = compileMessages(alertsByDevice.get(device));
        pager.alert(subject, message, device);
      }
    }
    alertsBySubject.clear();
    lastAlertTime = clock.get();
    sentFirstAlertThisBatch = false;
  }

  public synchronized Option<Duration> getTimeBeforeNextAlert() {
    return hasPendingAlerts() ? Option.some(getRemainingTime()) : Option.none();
  }

  @VisibleForTesting
  String compileMessages(Collection<PendingAlert> pendingAlerts) {
    StringBuilder messageBuilder = new StringBuilder();
    for (PendingAlert alert : pendingAlerts) {
      messageBuilder.append(alert.toString());
    }
    return messageBuilder.toString();
  }

  @VisibleForTesting
  Duration getRemainingTime() {
    DateTime currentTime = clock.get();
    Duration timeElapsedAfterLastAlert = new Duration(lastAlertTime, currentTime);
    if (timeElapsedAfterLastAlert.isLongerThan(duration)) {
      return Duration.ZERO;
    } else {
      return duration.minus(timeElapsedAfterLastAlert);
    }
  }

  @VisibleForTesting
  void addPendingAlerts(PendingAlert... pendingAlerts) {
    for (PendingAlert alert : pendingAlerts) {
      alertsBySubject.put(alert.getSubject(), alert);
    }
  }

  @VisibleForTesting
  boolean getSentFirstAlertThisBatch() {
    return sentFirstAlertThisBatch;
  }

  @VisibleForTesting
  void setLastAlertTime(DateTime lastAlertTime) {
    this.lastAlertTime = lastAlertTime;
  }

  static class PendingAlert {

    private String subject;
    private String message;
    private Option<Throwable> maybeThrowable;
    private Device device;

    PendingAlert(String subject, String message, Option<Throwable> maybeThrowable, Device device) {
      this.subject = subject;
      this.message = message;
      this.maybeThrowable = maybeThrowable;
      this.device = device;
    }

    String getSubject() {
      return subject;
    }

    String getMessage() {
      return message;
    }

    Option<Throwable> getMaybeThrowable() {
      return maybeThrowable;
    }

    Device getDevice() {
      return device;
    }

    @Override
    public String toString() {
      return maybeThrowable.isDefined() ?
          Strings.format("Message: %s; Throwable: %s; ", message, maybeThrowable.toStringOr("")) :
          Strings.format("Message: %s; ", message);
    }

  }

}
