package com.kaching.util.mail;

import org.apache.commons.mail.Email;
import org.apache.commons.mail.EmailException;
import org.apache.commons.mail.HtmlEmail;
import org.apache.commons.mail.SimpleEmail;

import com.google.inject.ImplementedBy;

@ImplementedBy(KachingEmailSender.class)
public interface EmailSender {

  /**
   * Send an email. Defaults to using email.getFromAddress() for the "envelope sender" bounce
   * return address.
   * <p/>
   * (Hint: {@link SimpleEmail} and {@link HtmlEmail} are the most
   * likely Email types you'll actually be passing in.)
   *
   * @param email {@link Email}
   * @return on success, returns a {@link String} identifier (typically an smtp server message id)
   */
  String send(TestableHtmlEmail email) throws EmailException;

  String send(Email email) throws EmailException;
}
