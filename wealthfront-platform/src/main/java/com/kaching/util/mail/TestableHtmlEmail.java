package com.kaching.util.mail;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Lists.newArrayListWithCapacity;
import static com.google.common.collect.Maps.newHashMap;
import static com.kaching.entities.EmailRecipient.NameType.PREFERRED;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.unmodifiableList;
import static java.util.Collections.unmodifiableMap;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.mail.internet.InternetAddress;

import org.apache.commons.mail.EmailException;
import org.apache.commons.mail.HtmlEmail;

import com.google.common.base.Predicate;
import com.kaching.entities.EmailAddress;
import com.kaching.entities.EmailRecipient;

public class TestableHtmlEmail extends HtmlEmail {

  private final Map<String, String> debugAttributes = newHashMap();

  private String description;

  public void addDebugAttribute(String key, String value) {
    debugAttributes.put(key, value);
  }

  public Map<String, String> getDebugAttributes() {
    return unmodifiableMap(debugAttributes);
  }

  public String getHtml() {
    return html;
  }

  public String getText() {
    return text;
  }

  public List<String> getToList() {
    return addressesToStrings(toList);
  }

  public List<String> getCcList() {
    return addressesToStrings(ccList);
  }

  public List<String> getBccList() {
    return addressesToStrings(bccList);
  }

  @SuppressWarnings("unchecked")
  private List<String> addressesToStrings(Collection<?> addresses) {
    List<String> list = newArrayListWithCapacity(addresses.size());
    for (InternetAddress address : (Collection<InternetAddress>) addresses) {
      list.add(address.getAddress());
    }
    return unmodifiableList(list);
  }

  public void addTo(EmailRecipient recipient) throws EmailException {
    addTo(recipient.getEmailAddress().toString(), recipient.getFirstNameOrFullName(PREFERRED));
  }

  public void addBcc(EmailRecipient recipient) throws EmailException {
    addBcc(recipient.getEmailAddress().toString(), recipient.getFirstNameOrFullName(PREFERRED));
  }

  public List<EmailAddress> removeRecipients(Predicate<EmailAddress> shouldRemove) {
    List<EmailAddress> removed = emptyList();
    for (Collection<?> recipientList : asList(toList, ccList, bccList)) {
      @SuppressWarnings("unchecked")
      Iterator<InternetAddress> recipients = (Iterator<InternetAddress>) recipientList.iterator();
      while (recipients.hasNext()) {
        InternetAddress recipient = recipients.next();
        EmailAddress address = new EmailAddress(recipient.getAddress());
        if (shouldRemove.apply(address)) {
          recipients.remove();
          if (removed.isEmpty()) {
            removed = newArrayList(address);
          } else {
            removed.add(address);
          }
        }
      }
    }
    return removed;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

}
