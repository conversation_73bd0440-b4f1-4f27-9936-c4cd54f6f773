package com.kaching.util.mail;

import static java.util.Collections.emptyList;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.common.collect.ImmutableList;
import com.google.inject.ImplementedBy;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.util.functional.Tuple3;

@ImplementedBy(PagerImpl.class)
public interface Pager {

  @ExposeType(value = {ExposeTo.BACKEND, ExposeTo.TAOS}, namespace = RewriteNamespace.DO_NOT_COPY)
  enum Device {
    @Deprecated
    PAGER_ACATS(
        "c9624be806dd44b1a5b95437e3c8e6ea",
        "acats",
        "<EMAIL>"),

    PAGER_ANALYTICS_ENGINEERING(
        "60bdce2c27c64105c0f4e68c9af7564b",
        "data-engineering",
        "<EMAIL>"),

    PAGER_ANALYTICS_ENGINEERING_NON_PROD(
        "0df50f70fac9480ed013c144a9188e7d",
        "data-engineering-nonprod",
        "<EMAIL>"),

    PAGER_ACATS_OPS(
        "746cf9f2ae7d4500d0e77ced66f01ac0",
        "acats-ops",
        "<EMAIL>"),

    PAGER_BANKING_PLATFORM(
        "63f0ea01497b43a7b0b2709132bc7deb",
        "banking-platform",
        "<EMAIL>"),

    PAGER_ADVICE_AUTOMATION(
        "8e7c1f052d89469bbd3ccf5ce0ff79be",
        "online-services",
        "<EMAIL>"),
    // Before moving LINKOPS to ONLINEOPS (SITEH-442) the pager for PAGER_ADVICE_AUTOMATION was:
    //    PAGER_ADVICE_AUTOMATION(
    //        "2d5044d354ed432aac9d675e54203385",
    //        "advice-automation",
    //        "<EMAIL>"),

    PAGER_ADVISOR_OPS(
        "a985b8a79cf04202c07a1cba7141086e",
        "advisor-ops",
        "<EMAIL>"),

    PAGER_AA_AND_LINKING(
        "8e7c1f052d89469bbd3ccf5ce0ff79be",
        "online-services",
        "<EMAIL>"),
    // Before moving LINKOPS to ONLINEOPS (SITEH-442) the pager for PAGER_AA_AND_LINKING was:
    //    PAGER_AA_AND_LINKING(
    //        ImmutableList.of("2d5044d354ed432aac9d675e54203385", "555074d1ff924df99926f2aa0ea5d09b"),
    //        "aa-and-linking",
    //        "<EMAIL>"),

    PAGER_ANDROID(
        "4735bb8d1cae4605d05fa886fe57e62f",
        "android",
        "<EMAIL>"),

    PAGER_API(
        "6464a3cb26e147198b2baaba8c25f17a",
        "api",
        "<EMAIL>"
    ),

    PAGER_AWS_ADMIN(
        "fb84d4e82a8d440598c043995ff416f5",
        "data",
        "<EMAIL>"
    ),

    PAGER_BACKEND_INFRA_LOW_PRIORITY(
        "656b71da61424f05c075af99f6f53474",
        "beinfra-low-priority",
        "<EMAIL>"),

    @Deprecated
    PAGER_BROKERAGE_BRIAN_DENNEN(
        emptyList(),
        null),

    PAGER_BROKERAGE_PLATFORM(
        "c9624be806dd44b1a5b95437e3c8e6ea",
        "brokerage-platform",
        "<EMAIL>"),

    PAGER_BROKERAGE_OPS(
        "f79d53e568e34863a0472c7484c511ab",
        "brokerage-ops",
        "<EMAIL>"),

    PAGER_BROKERAGE_OPS_AND_INVESTMENT_SERVICES(
        "2cbc7ce999944a07c0d50ca6414af250",
        "investment",
        "<EMAIL>"),

    PAGER_BROKERAGE_OPS_AND_BROKERAGE_PLATFORM(
        "84509ee8af384503d091586d5a44075d",
        "brokerage-all",
        "<EMAIL>"),

    PAGER_CASHIERING_OPS(
        "4479472ed557406da35e4e41df3fd0e0",
        "cashiering-ops",
        "<EMAIL>"
    ),

    PAGER_CASH_SETTLEMENT_OPS(
        "a8a5172fd0574608c00705a97a62443a",
        "cash-settlement-ops",
        "<EMAIL>"
    ),

    PAGER_CLIENT_REPORTING_OPS(
        "a79141f08c594a48b1a3b602bf5e2993",
        "client-reporting-ops",
        "<EMAIL>"
    ),

    PAGER_CONSOLIDATED_BREAKS(
        "b8eb612371314a2ca0b4c9d75fd9fe93",
        "consolidated-breaks",
        "<EMAIL>"),

    PAGER_DATA(
        "6c64544d54614d04ad89ceb89391fc0f",
        "data",
        "<EMAIL>"),

    PAGER_DATA_NONPROD(
        "71bbf71a40c34e18b13bdee24bdc0181",
        "data-nonprod",
        "<EMAIL>"),

    PAGER_DATA_LEAD_LOW_PRIORITY(
        "bb4c6b089dfc43aa944761594048ce8e",
        "data-ops-leads",
        "<EMAIL>"),

    PAGER_RESEARCH(
        "f9e3c13add1d4c41acbbe500f0659f11",
        "research",
        "<EMAIL>"),

    PAGER_DATA_SCIENCE(
        "5a57dde8258b4e9797a16640dfb24761",
        null),

    PAGER_DAVID(
        "0489e2f925e14652865c261ae06005b5",
        null,
        "<EMAIL>"),

    PAGER_FRACTIONAL_SHARES(
        "5ddb89b0ab22457d8f57496c842f15fb",
        "fractional-shares"
    ),

    PAGER_FRAUD_AND_RISK(
        "c67d9fcc01644be29c410b566df13ee2",
        "fraud-and-risk-eng-ops",
        "<EMAIL>"
    ),

    PAGER_FRAUD_AND_RISK_NON_URGENT(
        emptyList(),
        "fraud-and-risk-eng-nopage",
        "<EMAIL>"
    ),

    PAGER_FRAUD_OPS(
        "1d47483a249d4b0dd02f0e788460b152",
        "fraud-ops",
        "<EMAIL>"
    ),

    PAGER_FUND_OPERATIONS(
        "fe05e58c8f8847058a21a01fa1a08edc",
        "fund-operations",
        "<EMAIL>"
    ),

    PAGER_GD_ACCOUNT_LIFECYCLE(
        "df6beab9f25f44b69d697adce2a3c2d0",
        "gd-account-lifecycle"
    ),

    PAGER_GROWTH(
        "9b0d9a7894854e24a576f1f724255c6f",
        "growth"
    ),

    PAGER_HYOMIN(
        "3bacab682b014ca7afd606421a6c6e9b",
        null,
        "<EMAIL>"),

    PAGER_INVESTMENT_SERVICES(
        "c7f70b61b39643888f0dbb02dd931453",
        "investment",
        "<EMAIL>"),

    PAGER_INVESTMENT_PRODUCTS(
        "1bb3d10ca4f6470bd0818a8a05c285d4",
        "investment-products",
        "<EMAIL>"),

    PAGER_IOS(
        "ecdf15492f3c404b883264dfde57293c",
        "ios",
        "<EMAIL>"),

    PAGER_IT(
        "5629952ae89a490fd02106a6744a6fa5",
        "it",
        "<EMAIL>"),

    PAGER_JIAQI(
        "9a8a833bbf684d3db30c3761c3ed4af7",
        null,
        "<EMAIL>"),

    PAGER_INVESTMENT_RESEARCH(
        "f6283e9c58734a07d0f4f7f770ea62bb",
        "investment-research"),

    PAGER_INVESTMENT_SERVICES_NON_URGENT(
        emptyList(),
        "investment-nopage",
        "<EMAIL>"),

    PAGER_INVESTMENT_SERVICES_OFFICE_HOURS(
        "9a7517171b44470dd03c60936d6c6a5e",
        "investment",
        "<EMAIL>"),

    PAGER_LINKING_SERVICES(
        "8e7c1f052d89469bbd3ccf5ce0ff79be",
        "online-services",
        "<EMAIL>"),

    PAGER_LENDING_PLATFORM(
        "d2e4f32f98514805c0aa9375186f3efb",
        "lending-platform",
        "<EMAIL>"
    ),
    PAGER_NEW_ACCOUNT_OPS(
        "8bf083a682a54206d078314b491dac83",
        "new-account-bops-ops",
        "<EMAIL>"),

    PAGER_NEW_PRODUCT_1(
        "eba3016d6dd74895b6a423c3c3bd8c50",
        "new-product-1"),

    PAGER_ONLINE_SERVICES(
        "8e7c1f052d89469bbd3ccf5ce0ff79be",
        "online-services",
        "<EMAIL>"),

    PAGER_OPS(
        "5f207fa6ea334036bd9f171831dcabd3",
        "ops",
        "<EMAIL>"),

    PAGER_SECURITY(
        "27912602b7a74c0fa9de821efe61555f",
        null,
        "<EMAIL>"),

    PAGER_SHARED_SERVICES(
        "d2187a6d5c344d03d0087676b8405f28",
        "shared-services",
        "<EMAIL>"
    ),

    PAGER_SITE_HEALTH(
        "e6144112b89a4b00d0c34fdceb919356",
        "site-health",
        "<EMAIL>"
    ),

    PAGER_SITE_HEALTH_AND_ONLINE_SERVICES(
        ImmutableList.of("e6144112b89a4b00d0c34fdceb919356", "8e7c1f052d89469bbd3ccf5ce0ff79be"),
        "site-health-and-online-services",
        "<EMAIL>"
    ),

    PAGER_SITE_OUTAGE_ESCALATIONS(
        "1f1d9cfa85444106d05f217f94947b47",
        null
    ),

    PAGER_SYSTEMS(
        "5f207fa6ea334036bd9f171831dcabd3",
        "ops",
        "<EMAIL>"),

    PAGER_TAOS(
        "db4171183f3a49aebb0d23f2ddc48c74",
        "frontend",
        "<EMAIL>"),

    PAGER_TESTING_FAKE(
        "971f5ae75d1449e8a40177c58549cd63",
        null),

    PAGER_TRADING_OPS(
        "79f7183c04a74700c0c1b7cfc9dc83d2",
        "trading-ops",
        "<EMAIL> "
    ),

    PAGER_TRADING_PRODUCTS(
        "10f5b68d31504400d01ae4946eb4a6e1",
        "trading-products",
        "<EMAIL>"
    ),

    PAGER_WEB(
        "ea1757be6196455a8344636cfb7f1208",
        "frontend",
        "<EMAIL>"),

    PAGER_WEB_NON_URGENT(
        emptyList(),
        "frontend",
        "<EMAIL>"),

    PAGER_WINDOWS(
        "56c765046bb2433f97771cb6097c4db6",
        "windows",
        "<EMAIL>"),

    PAGER_ZENDESK(
        emptyList(),
        "zendesk"),

    PAGER_NETWORKING(
        "76bf2ff67f6b4409c0705fb3ebabd6d4",
        "networking",
        "<EMAIL>"),

    PAGER_TRADE_VALIDATION(
        "91098b0feabb4c05c091e81657e12b88",
        "trade-validation");

    private static final Map<String, Device> icingaGroupMap = new HashMap<>();

    static {
      for (Device device : values()) {
        if (device.getIcingaGroup() == null) {
          continue;
        }
        icingaGroupMap.put(device.getIcingaGroup(), device);
      }
    }

    private final List<String> pagerdutyApiKeys;
    private final String icingaGroup;
    private final String[] emailAddress;

    Device(String pagerdutyApiKeys, String icingaGroup, String... emailAddress) {
      this.pagerdutyApiKeys = ImmutableList.of(pagerdutyApiKeys);
      this.icingaGroup = icingaGroup;
      this.emailAddress = emailAddress;
    }

    Device(List<String> pagerdutyApiKeys, String icingaGroup, String... emailAddress) {
      this.pagerdutyApiKeys = pagerdutyApiKeys;
      this.icingaGroup = icingaGroup;
      this.emailAddress = emailAddress;
    }

    public List<String> getPagerDutyApiKeys() {
      return pagerdutyApiKeys;
    }

    public List<String> getEmailAddresses() {
      return Arrays.asList(emailAddress);
    }

    public String getIcingaGroup() {
      return icingaGroup;
    }

    public static Device fromIcingaGroup(String icingaGroup) {
      return icingaGroupMap.get(icingaGroup);
    }

  }

  enum MetadataKey {

    HOST("host"),
    CREATED_AT("created at"),
    DELAYED_UNTIL("delayed until"),
    JIRA_COMPONENT("jira component");

    private final String key;

    MetadataKey(String key) {
      this.key = key;
    }

    public String getKey() {
      return key;
    }

  }

  void alert(String subject, String message, Throwable t, Device device);

  void alert(String subject, String message, Device device);

  void resolve(String subject, String message, Pager.Device device);

  /**
   * Broken: BEINFRA-1202. Use {@link #resolve(String, String, Device)} unconditionally.
   */
  @Deprecated
  void resolveIfOpen(String subject, String message, Pager.Device device);

  /**
   * Broken: BEINFRA-1202. Use {@link #resolve(String, String, Device)} unconditionally.
   */
  @Deprecated
  void resolveIfOpen(Set<Tuple3<String, String, Pager.Device>> alerts);

  /**
   * Incident titles are not the same as the incident key used for idempotency.
   * At best, you can try checking for a subject within the returned messages.
   * <p>
   * Example:
   * Pager.alert("My Subject", "My Super Long Message", Device.TESTING);
   * Pager.getOpenIncidentTitlesBestEffort() -> List.of(
   * "My Subject: My Super..."
   * )
   *
   * @return A possibly incomplete list of incident titles
   */
  default List<String> getOpenIncidentTitlesBestEffort() {
    return emptyList();
  }

  Pager SILENT_PAGER = new Pager() {
    @Override
    public void alert(String subject, String message, Device device) {
    }

    @Override
    public void alert(String subject, String message, Throwable t, Device device) {
    }

    @Override
    public void resolve(String subject, String message, Device device) {
    }

    @Override
    public void resolveIfOpen(String subject, String message, Device device) {
    }

    @Override
    public void resolveIfOpen(Set<Tuple3<String, String, Pager.Device>> alerts) {
    }

  };

  Pager ERROR_PAGER = new Pager() {
    @Override
    public void alert(String subject, String message, Device device) {
      throw new UnsupportedOperationException();
    }

    @Override
    public void alert(String subject, String message, Throwable t, Device device) {
      throw new UnsupportedOperationException(t);
    }

    @Override
    public void resolve(String subject, String message, Device device) {
      throw new UnsupportedOperationException();
    }

    @Override
    public void resolveIfOpen(String subject, String message, Device device) {
      throw new UnsupportedOperationException();
    }

    @Override
    public void resolveIfOpen(Set<Tuple3<String, String, Device>> alerts) {
      throw new UnsupportedOperationException();
    }

  };

}
