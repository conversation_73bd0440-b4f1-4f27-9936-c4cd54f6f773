package com.kaching.util.id;

import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;

public abstract class AbstractIdExternalizer<T extends HibernateEntity> implements IdExternalizer<T> {

  @Override
  public abstract String externalize(Id<T> internal);

  @Override
  public ExternalId<T> externalizeId(Id<T> internal) {
    if (internal == null) {
      return null;
    }
    return new ExternalId<>(externalize(internal));
  }

  @Override
  public abstract Option<Id<T>> internalize(String externalized);

  @Override
  public Option<Id<T>> internalize(ExternalId<T> externalized) {
    if (externalized == null) {
      return Option.none();
    }
    return internalize(externalized.getId());
  }

}
