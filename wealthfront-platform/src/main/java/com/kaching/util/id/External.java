package com.kaching.util.id;

import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.converters.ConvertedBy;
import com.kaching.platform.converters.NullHandlingConverter;
import com.twolattes.json.Json;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.types.NullSafeType;

@ConvertedBy(External.Converter.class)
@MarshalledBy(External.JsonType.class)
public class External<T extends Identifier<Long>> extends AbstractIdentifier<String> {

  public External(String id) {
    super(id);
  }

  public static class Converter extends NullHandlingConverter<External<?>> {

    @Override
    public External<?> fromNonNullableString(String representation) {
      return new External<>(representation);
    }

    @Override
    public String nonNullableToString(External<?> value) {
      return value.getId();
    }

  }

  public static class JsonType extends NullSafeType<External<?>, Json.String> {

    @Override
    public Json.String nullSafe<PERSON><PERSON>hall(External<?> entity) {
      return Json.string(entity.getId());
    }

    @Override
    public External<?> nullSafeUnmarshall(Json.String object) {
      return new External<>(object.getString());
    }

  }

}
