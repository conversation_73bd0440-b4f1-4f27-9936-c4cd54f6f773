package com.kaching.util.id;

import java.lang.reflect.Constructor;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Optional;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Thunk;
import com.wealthfront.crypto.id.LongExternalizerImpl;

public class LongIdExternalizer<T extends Identifier<Long>> extends AbstractIdentifierExternalizer<T> {

  @VisibleForTesting
  protected final LongExternalizerImpl longExternalizer;

  private final Class<T> clazz;
  private final Thunk<Constructor<T>> constructorThunk = Thunk.thunk(() -> {
    Constructor<T> longConstructor = getLongConstructor();
    longConstructor.setAccessible(true);
    return longConstructor;
  });

  public LongIdExternalizer(Class<T> clazz, String prefix, String password) {
    longExternalizer = new LongExternalizerImpl(prefix, password);
    this.clazz = clazz;
  }

  @Override
  public String externalize(T internal) {
    if (internal == null) {
      return null;
    }

    return longExternalizer.externalize(internal.getId());
  }

  @Override
  T instantiateNewIdentifier(long value) {
    try {
      return constructorThunk.get().newInstance(value);
    } catch (Exception ex) {
      throw new RuntimeException(ex);
    }
  }

  @Override
  public Option<T> internalize(String externalized) {
    if (externalized == null) {
      return Option.none();
    }
    Optional<Long> longValue = longExternalizer.internalize(externalized);
    if (longValue.isPresent()) {
      return Option.some(instantiateNewIdentifier(longValue.get()));
    } else {
      return Option.none();
    }
  }

  private Constructor<T> getLongConstructor() {
    try {
      return clazz.getDeclaredConstructor(long.class);
    } catch (NoSuchMethodException e) {
      try {
        return clazz.getDeclaredConstructor(Long.class);
      } catch (ReflectiveOperationException ex) {
        throw new RuntimeException(e);
      }
    }
  }

}
