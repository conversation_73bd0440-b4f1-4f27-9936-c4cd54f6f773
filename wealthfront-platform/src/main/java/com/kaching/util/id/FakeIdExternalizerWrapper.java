package com.kaching.util.id;

import static com.google.common.base.Preconditions.checkArgument;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.AbstractHibernateEntity;
import com.kaching.platform.hibernate.Id;

public class FakeIdExternalizerWrapper<T extends AbstractHibernateEntity> implements IdExternalizer<T> {

  private final IdExternalizer<T> delegate;
  private final String prefix;

  public FakeIdExternalizerWrapper(String prefix, IdExternalizer<T> delegate) {
    this.delegate = delegate;
    this.prefix = prefix + "-";
  }

  @Override
  public String externalize(Id<T> internal) {
    return outgoing(delegate.externalize(internal));
  }

  @Override
  public ExternalId<T> externalizeId(Id<T> internal) {
    return new ExternalId<>(outgoing(delegate.externalizeId(internal).getId()));
  }

  @Override
  public Option<Id<T>> internalize(String externalized) {
    if (externalized.length() != 19 + prefix.length()) {
      return Option.none();
    }
    return delegate.internalize(incoming(externalized));
  }

  @Override
  public Option<Id<T>> internalize(ExternalId<T> externalized) {
    if (externalized.getId().length() != 19 + prefix.length()) {
      return Option.none();
    }
    return delegate.internalize(new ExternalId<>(incoming(externalized.getId())));
  }

  @VisibleForTesting
  String incoming(String string) {
    return string.substring(prefix.length());
  }

  @VisibleForTesting
  String outgoing(String string) {
    checkArgument(string.length() == 19);
    return prefix + string;
  }

}
