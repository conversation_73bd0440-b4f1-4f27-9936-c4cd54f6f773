package com.kaching.util.id;

import com.google.common.base.CharMatcher;
import com.kaching.platform.converters.NullHandlingConverter;

public abstract class FlexIdConverter<T extends FlexId> extends NullHandlingConverter<T> {

  @Override
  protected T fromNonNullableString(String s) {
    if (CharMatcher.digit().matchesAllOf(s)) {
      return fromInternalId(s);
    } else if (s.matches(ExternalId.PATTERN)) {
      return fromExternalId(s);
    } else {
      throw new IllegalArgumentException(s + " is not a valid FlexId");
    }
  }

  @Override
  protected String nonNullableToString(T flexId) {
    return flexId.toString();
  }

  public abstract T fromInternalId(String s);

  public abstract T fromExternalId(String s);

}
