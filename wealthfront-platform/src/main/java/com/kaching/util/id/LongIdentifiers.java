package com.kaching.util.id;

import static java.lang.Long.parseLong;

import com.google.common.base.Function;
import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.common.AbstractLongIdentifier;
import com.kaching.platform.converters.NullHandlingConverter;
import com.twolattes.json.Json;
import com.twolattes.json.types.NullSafeType;

public class LongIdentifiers {

  public abstract static class Converter<T extends AbstractIdentifier<Long>> extends NullHandlingConverter<T> {

    protected abstract Function<Long, T> constructor();

    @Override
    protected T fromNonNullableString(String s) {
      return constructor().apply(parseLong(s));
    }

    @Override
    protected String nonNullableToString(T t) {
      return t.toString();
    }

  }

  public abstract static class LongConverter<T extends AbstractLongIdentifier> extends NullHandlingConverter<T> {

    protected abstract T construct(long id);

    @Override
    protected T fromNonNullableString(String s) {
      return construct(parseLong(s));
    }

    @Override
    protected String nonNullableToString(T t) {
      return t.toString();
    }

  }

  public abstract static class JsonType<T extends AbstractIdentifier<Long>> extends NullSafeType<T, Json.Number> {

    protected abstract Function<Long, T> constructor();

    @Override
    protected Json.Number nullSafeMarshall(T entity) {
      return Json.number(entity.getId());
    }

    @Override
    protected T nullSafeUnmarshall(Json.Number number) {
      return constructor().apply(number.getNumber().longValueExact());
    }

  }

  public abstract static class LongJsonType<T extends AbstractLongIdentifier> extends NullSafeType<T, Json.Number> {

    protected abstract T construct(long id);

    @Override
    protected Json.Number nullSafeMarshall(T entity) {
      return Json.number(entity.asLong());
    }

    @Override
    protected T nullSafeUnmarshall(Json.Number number) {
      return construct(number.getNumber().longValueExact());
    }

  }

}
