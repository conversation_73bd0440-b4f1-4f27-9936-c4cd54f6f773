package com.kaching.util.id;

import static com.google.common.base.Preconditions.checkArgument;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.common.Option;

public class FakeLongIdExternalizerWrapper<T extends Identifier<Long>> extends AbstractIdentifierExternalizer<T> {

  private final String prefix;
  private final LongIdExternalizer<T> delegate;

  public FakeLongIdExternalizerWrapper(String prefix, LongIdExternalizer<T> delegate) {
    this.prefix = prefix + "-";
    this.delegate = delegate;
  }

  @Override
  public Option<T> internalize(String externalized) {
    if (externalized.length() != 19 + prefix.length()) {
      return Option.none();
    }
    return delegate.internalize(incoming(externalized));
  }

  @Override
  public String externalize(T internal) {
    return outgoing(delegate.externalize(internal));
  }

  @Override
  T instantiateNewIdentifier(long value) {
    return delegate.instantiateNewIdentifier(value);
  }

  @VisibleForTesting
  String incoming(String string) {
    return string.substring(prefix.length());
  }

  @VisibleForTesting
  String outgoing(String string) {
    checkArgument(string.length() == 19);
    return prefix + string;
  }

}
