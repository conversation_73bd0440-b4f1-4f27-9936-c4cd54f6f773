package com.kaching.util.id;

import com.google.common.base.Function;
import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.converters.NullHandlingConverter;
import com.twolattes.json.Json;
import com.twolattes.json.types.NullSafeType;

public class StringIdentifiers {

  public abstract static class Converter<T extends AbstractIdentifier<String>> extends NullHandlingConverter<T> {

    protected abstract Function<String, T> constructor();

    @Override
    protected T fromNonNullableString(String s) {
      return constructor().apply(s);
    }

    @Override
    protected String nonNullableToString(T t) {
      return t.getId();
    }

  }

  public abstract static class JsonType<T extends AbstractIdentifier<String>> extends NullSafeType<T, Json.String> {

    protected abstract Function<String, T> constructor();

    @Override
    protected Json.String nullSafeMarshall(T entity) {
      return Json.string(entity.getId());
    }

    @Override
    protected T nullSafeUnmarshall(Json.String string) {
      return constructor().apply(string.getString());
    }

  }

}
