package com.kaching.util.id;

import static java.lang.Integer.parseInt;

import java.util.function.Function;

import com.kaching.platform.common.Identifier;
import com.kaching.platform.converters.NullHandlingConverter;
import com.twolattes.json.Json;
import com.twolattes.json.types.NullSafeType;

public class IntIdentifiers {

  public abstract static class IntConverter<T extends Identifier<Integer>> extends NullHandlingConverter<T> {

    protected abstract Function<Integer, T> constructor();

    @Override
    protected T fromNonNullableString(String s) {
      return constructor().apply(parseInt(s));
    }

    @Override
    protected String nonNullableToString(T t) {
      return t.toString();
    }

  }

  public abstract static class IntJsonType<T extends Identifier<Integer>> extends NullSafeType<T, Json.Number> {

    protected abstract Function<Integer, T> constructor();

    @Override
    protected Json.Number nullSafeMarshall(T entity) {
      return Json.number(entity.getId());
    }

    @Override
    protected T nullSafeUnmarshall(Json.Number number) {
      return constructor().apply(number.getNumber().intValueExact());
    }

  }

}
