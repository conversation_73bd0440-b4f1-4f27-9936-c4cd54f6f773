package com.kaching.util.id;

import static com.google.common.base.Strings.isNullOrEmpty;
import static com.kaching.util.Preconditions.checkNotNullOrEmptyString;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.CharMatcher;
import com.kaching.platform.common.AbstractIdentifier;
import com.kaching.platform.converters.ConvertedBy;
import com.kaching.platform.converters.NullHandlingConverter;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.twolattes.json.Json;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.types.NullSafeType;

@ConvertedBy(FlexId.Converter.class)
@MarshalledBy(FlexId.JsonType.class)
public class FlexId<T extends HibernateEntity> extends AbstractIdentifier<String> {

  public FlexId(long id) {
    super(flexify(id));
  }
  
  public FlexId(String id) {
    super(checkNotNullOrEmptyString(id));
  }
  
  public FlexId(Id<T> id) {
    super(Long.toString(id.getId()));
  }
  
  public FlexId(ExternalId<T> id) {
    super(id.getId());
  }
  
  public static <H extends HibernateEntity> FlexId<H> of(String id) {
    return new FlexId<>(id);
  }

  public static String flexify(Id<?> id) {
    return flexify(id.getId());
  }

  public static String flexify(ExternalId<?> id) {
    return id.getId();
  }

  public static String flexify(long id) {
    return Long.toString(id);
  }

  @Override
  public int compareTo(AbstractIdentifier<String> that) {
    throw new UnsupportedOperationException();
  }
  
  public <R> R visit(Visitor<R, T> visitor) {
    if (isInternalId()) {
      return visitor.caseInternal(asInternalId());
    }
    return visitor.caseExternal(asExternalId());
  }
  
  public interface Visitor<R, H extends HibernateEntity> {
    
    R caseInternal(Id<H> id);
    
    R caseExternal(ExternalId<H> id);
    
  }

  public Id<T> getAsId(IdExternalizer<T> externalizer) {
    if (isInternalId()) {
      return asInternalId();
    } else {
      return externalToInternal(externalizer);
    }
  }

  public ExternalId<T> getAsExternalId(IdExternalizer<T> externalizer) {
    if (isInternalId()) {
      return internalToExternal(externalizer);
    } else {
      return asExternalId();
    }
  }

  public void checkValidity(IdExternalizer<T> externalizer) {
    if (isInternalId()) {
      getAsExternalId(externalizer);
    } else {
      getAsId(externalizer);
    }
  }

  @VisibleForTesting
  boolean isInternalId() {
    return !isNullOrEmpty(getId()) && CharMatcher.inRange('0', '9').matchesAllOf(getId());
  }

  @VisibleForTesting
  private ExternalId<T> internalToExternal(IdExternalizer<T> externalizer) {
    return externalizer.externalizeId(asInternalId());
  }

  @VisibleForTesting
  private ExternalId<T> asExternalId() {
    return new ExternalId<>(getId());
  }

  @VisibleForTesting
  private Id<T> asInternalId() {
    try {
      return Id.of(getId());
    } catch (NumberFormatException e) {
      throw new InvalidArgumentException("Expected a number as an internal identifier", e);
    }
  }

  @VisibleForTesting
  private Id<T> externalToInternal(IdExternalizer<T> externalizer) {
    return externalizer.internalize(getId()).getOrThrow(new InvalidArgumentException("Expected an external identifier."));
  }

  public static class Converter extends NullHandlingConverter<FlexId<?>> {

    @Override
    protected FlexId<?> fromNonNullableString(String s) {
      return new FlexId<>(s);
    }

    @Override
    protected String nonNullableToString(FlexId<?> externalId) {
      return externalId.getId();
    }
    
  }

  public static class JsonType extends NullSafeType<FlexId<?>, Json.String> {

    @Override
    public Json.String nullSafeMarshall(FlexId<?> flexId) {
      return Json.string(flexId.getId());
    }

    @Override
    public FlexId<?> nullSafeUnmarshall(Json.String string) {
      return new FlexId<>(string.getString());
    }
    
  }

}
