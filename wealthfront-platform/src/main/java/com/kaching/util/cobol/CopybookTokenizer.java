package com.kaching.util.cobol;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Sets.newHashSet;
import static java.lang.Character.isWhitespace;

import java.io.PushbackReader;
import java.io.Reader;
import java.util.List;
import java.util.Set;

import com.kaching.platform.functional.Unchecked;
import com.kaching.util.io.UncheckedIo;

/**
 * Lexical analysis breaks the source code text into small pieces called tokens.
 */
class CopybookTokenizer {

  List<String> tokenize(Reader reader) {
    return tokenize(new PushbackReader(reader));
  }

  List<String> tokenize(PushbackReader reader) {
    List<String> tokens = newArrayList();
    while (!eof(reader)) {
      // skip first 6 columns
      Column column = new Column();
      while (column.get() < 6) {
        read(reader, column);
      }

      // ignore comments
      char c = read(reader, column);
      if (c == '*') {
        consumeLine(reader, column);
        continue;
      } else {
        unread(reader, column, c);
      }

      StringBuilder builder = new StringBuilder();
      boolean literal = false;
      while (column.get() < 72) {
        c = read(reader, column);
        if (c == '\'') {
          literal = !literal;
        }
        if (isWhitespace(c)) {
          if (literal) {
            builder.append(c);
          } else {
            realize(tokens, builder);
          }
        } else if (c == '.') {
          realize(tokens, builder);
          realize(tokens, ".");
        } else {
          builder.append(c);
        }
      }

      consumeLine(reader, column);
    }
    return tokens;
  }

  private static void realize(List<String> tokens, StringBuilder builder) {
    String token = builder.toString();
    builder.delete(0, builder.length());
    realize(tokens, token);
  }

  private static void realize(List<String> tokens, String token) {
    if (!token.isEmpty()) {
      tokens.add(token);
    }
  }

  private static void consumeLine(Reader reader, Column column) {
    Set<Character> eol = newHashSet('\n', (char) -1);
    while (!eol.contains(read(reader, column))) {
      // consume
    }
  }

  private static char read(Reader reader, Column column) {
    column.increment();
    return Unchecked.get(() -> (char) reader.read());
  }

  private static void unread(PushbackReader reader, Column column, char c) {
    column.decrement();
    UncheckedIo.run(() -> reader.unread(c));
  }

  private static boolean eof(PushbackReader reader) {
    int c = UncheckedIo.get(reader::read);
    UncheckedIo.run(() -> reader.unread(c));
    return c == -1;
  }

  private static class Column {

    private int i = 0;

    int get() {
      return i;
    }

    void increment() {
      i++;
    }

    void decrement() {
      i--;
    }

  }

}
