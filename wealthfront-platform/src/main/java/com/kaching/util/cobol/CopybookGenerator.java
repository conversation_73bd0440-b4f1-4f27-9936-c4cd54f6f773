package com.kaching.util.cobol;

import static com.google.common.collect.Lists.transform;
import static com.google.common.collect.Maps.newHashMap;
import static java.util.Collections.singletonMap;

import java.util.Map;

import com.google.common.base.Function;
import com.google.common.io.ByteSource;
import com.kaching.platform.common.Pair;
import com.kaching.util.cobol.AST.EntryVisitor;
import com.kaching.util.cobol.AST.Group;
import com.kaching.util.cobol.AST.Picture;
import com.kaching.util.cobol.AST.RedefiningGroup;
import com.kaching.util.cobol.CopybookImpl.Field;

class CopybookGenerator {

  CopybookImpl generate(AST.Copybook copybook, ByteSource source) {
    return new CopybookImpl(getFields(copybook), calculateRecordLength(copybook), source);
  }

  private int calculateRecordLength(AST.Entry entry) {
    return entry.visit(new EntryVisitor<Integer>() {
      @Override
      public Integer caseCopybook(AST.Copybook copybook) {
        int length = 0;
        for (AST.Entry entry : copybook.entries) {
          length += calculateRecordLength(entry);
        }
        return length;
      }

      @Override
      public Integer casePicture(Picture picture) {
        return picture.getLength();
      }

      @Override
      public Integer caseGroup(Group group) {
        int length = 0;
        for (AST.Entry entry : group.entries) {
          length += calculateRecordLength(entry);
        }
        return length * group.occurs;
      }

      @Override
      public Integer caseRedefiningGroup(RedefiningGroup group) {
        return 0;
      }
    });
  }

  private Map<String, Field> getFields(AST.Entry entry) {
    return entry.visit(new EntryVisitor<Map<String, Field>>() {
      @Override
      public Map<String, Field> caseCopybook(AST.Copybook copybook) {
        Map<String, Field> fields = newHashMap();
        for (AST.Entry entry : copybook.entries) {
          fields.putAll(getFields(entry));
        }
        return fields;
      }

      @Override
      public Map<String, Field> casePicture(final Picture picture) {
        return singletonMap(
            picture.name,
            new Field(
                picture.type,
                transform(picture.offsets, new Function<Integer, Pair<Integer, Integer>>() {
                  @Override
                  public Pair<Integer, Integer> apply(Integer offset) {
                    return Pair.of(offset, offset + picture.getLength());
                  }
                })));
      }

      @Override
      public Map<String, Field> caseGroup(Group group) {
        Map<String, Field> fields = newHashMap();
        for (AST.Entry entry : group.entries) {
          fields.putAll(getFields(entry));
        }
        return fields;
      }

      @Override
      public Map<String, Field> caseRedefiningGroup(RedefiningGroup group) {
        return caseGroup(group);
      }
    });
  }

}
