package com.kaching.util.ip;

import com.google.common.collect.Range;
import com.google.common.collect.RangeSet;
import com.google.common.collect.TreeRangeSet;
import com.google.common.net.InetAddresses;

/*
 * https://wiki.wlth.fr/display/EDU/Wealthfront+Public+IP+Addresses
 */
public class WfOfficeIps {

  private static final RangeSet<String> IP_SET = getWfOfficeIps();

  private static RangeSet<String> getWfOfficeIps() {
    RangeSet<String> officeIps = TreeRangeSet.create();
    officeIps.add(Range.closed("***********", "***********"));
    officeIps.add(Range.closed("************", "************"));
    officeIps.add(Range.closed("**************", "**************"));
    officeIps.add(Range.closed("*************", "*************"));
    officeIps.add(Range.closed("************", "************"));
    return officeIps;
  }

  public static boolean isWfOfficeIp(String ip) {
    return IP_SET.contains(InetAddresses.forString(ip).getHostAddress());
  }

}
