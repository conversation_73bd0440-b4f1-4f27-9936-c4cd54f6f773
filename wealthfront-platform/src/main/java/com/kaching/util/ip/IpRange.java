package com.kaching.util.ip;

import java.net.InetAddress;

import com.google.common.net.InetAddresses;
import com.kaching.platform.common.Strings;

public class IpRange {

  private final long startInclusive;
  private final long endInclusive;

  private IpRange(long startInclusive, long endInclusive) {
    this.startInclusive = startInclusive;
    this.endInclusive = endInclusive;
  }

  public static IpRange of(String startIpInclusive, String endIpInclusive) {
    InetAddress startIp = InetAddresses.forString(startIpInclusive);
    long start = ipToLong(startIp);
    InetAddress endIp = InetAddresses.forString(endIpInclusive);
    long end = ipToLong(endIp);
    if (start > end) {
      throw new IllegalArgumentException(
          Strings.format("Start ip=%s is greater than end ip=%s", startIpInclusive, endIpInclusive));
    }
    return new IpRange(ipToLong(startIp), ipToLong(endIp));
  }

  public boolean contains(String ip) {
    InetAddress checkIp = InetAddresses.forString(ip);
    long check = ipToLong(checkIp);
    return startInclusive <= check && check <= endInclusive;
  }

  private static long ipToLong(InetAddress ip) {
    byte[] octets = ip.getAddress();
    long result = 0;
    for (byte octet : octets) {
      result <<= 8;
      result |= octet & 0xff;
    }
    return result;
  }

}
