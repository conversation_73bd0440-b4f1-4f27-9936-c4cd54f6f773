package com.kaching.util.ip;

import java.net.InetAddress;
import java.util.Set;

import com.google.common.collect.ImmutableSet;
import com.google.common.net.InetAddresses;

public class IpSet {

  private final Set<InetAddress> ipSet;

  public IpSet(String... ipAddresses) {
    ImmutableSet.Builder<InetAddress> ips = ImmutableSet.builder();
    for (String ipString : ipAddresses) {
      ips.add(InetAddresses.forString(ipString));
    }
    ipSet = ips.build();
  }

  public boolean contains(String ipAddressStr) {
    InetAddress ip = InetAddresses.forString(ipAddressStr);
    return ipSet.contains(ip);
  }

}
