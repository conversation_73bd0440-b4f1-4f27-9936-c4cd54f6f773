package com.kaching.util.io;

import static com.wealthfront.crypto.id.Bytes.byteArrayToInt;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import com.google.common.collect.Lists;

public class ListReader<T> {

  public interface Deserializer<T> {

    T deserialize(byte[] bytes) throws IOException;
  }

  private final int expectedSize;
  private final InputStream stream;
  private final Deserializer<T> deserializer;

  public ListReader(InputStream stream, ListReader.Deserializer<T> deserializer) {
    this(stream, 100, deserializer);
  }

  public ListReader(
      InputStream stream, int expectedSize, ListReader.Deserializer<T> deserializer) {
    this.stream = stream;
    this.expectedSize = expectedSize;
    this.deserializer = deserializer;
  }

  public List<T> read() throws IOException {
    List<T> list = Lists.newArrayListWithExpectedSize(expectedSize);
    byte[] size = new byte[4];
    int sizeRead = stream.read(size);
    byte[] buffer;
    int messageSize = byteArrayToInt(size);
    while (sizeRead > 0) {
      buffer = new byte[messageSize];
      stream.read(buffer);
      list.add(deserializer.deserialize(buffer));
      sizeRead = stream.read(size);
      messageSize = byteArrayToInt(size);
    }
    return list;
  }

  public static <T> ListReader<T> of(
      InputStream stream, Deserializer<T> deserializer) {
    return new ListReader<>(stream, deserializer);
  }

  public static <T> ListReader<T> of(
      InputStream stream, int expectedSize, Deserializer<T> deserializer) {
    return new ListReader<>(stream, expectedSize, deserializer);
  }

}
