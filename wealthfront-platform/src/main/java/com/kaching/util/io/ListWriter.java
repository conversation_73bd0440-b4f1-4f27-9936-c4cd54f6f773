package com.kaching.util.io;

import static com.wealthfront.crypto.id.Bytes.intToByteArray;

import java.io.IOException;
import java.io.OutputStream;

public class ListWriter<S extends OutputStream, T> {

  private final S outputStream;
  private final Iterable<T> list;
  private final Serializer<T> serializer;

  public interface Serializer<T> {

    byte[] serialize(T t) throws IOException;
  }

  private ListWriter(
      S outputStream, Iterable<T> list, Serializer<T> serializer) {
    this.outputStream = outputStream;
    this.list = list;
    this.serializer = serializer;
  }

  public S write() throws IOException {
    for (T t : list) {
      byte[] bytes = serializer.serialize(t);
      if (bytes != null) {
        outputStream.write(intToByteArray(bytes.length));
        outputStream.write(bytes);
      }
    }
    return outputStream;
  }

  public static <S extends OutputStream, T> ListWriter<S, T> of(
      S outputStream, Iterable<T> list, Serializer<T> serializer) {
    return new ListWriter<>(outputStream, list, serializer);
  }

}
