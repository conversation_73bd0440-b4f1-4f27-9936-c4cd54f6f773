package com.kaching.util.types;

import com.google.inject.Singleton;
import com.kaching.platform.converters.NullHandlingConverter;

@Singleton
public class IntegerConverter extends NullHandlingConverter<Integer> {

  @Override
  protected Integer fromNonNullableString(String representation) {
    return Integer.valueOf(representation);
  }

  @Override
  protected String nonNullableToString(Integer value) {
    return value.toString();
  }

}
