package com.kaching.util.types;

import com.kaching.platform.converters.NullHandlingConverter;
import com.twolattes.json.Json;

public class JsonArrayConverter extends NullHandlingConverter<Json.Array> {

  @Override
  protected Json.Array fromNonNullableString(String representation) {
    return (Json.Array) Json.fromString(representation);
  }

  @Override
  protected String nonNullableToString(Json.Array value) {
    return value.toString();
  }

}
