package com.kaching.util.types;

import static java.lang.String.format;

import java.lang.reflect.Modifier;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

import com.kaching.platform.common.Option;

public class TypesFormatter {

  public static String getSimpleName(Type type) {
    if (type instanceof Class<?>) {
      return ((Class<?>) type).getSimpleName();
    } else if (type instanceof ParameterizedType) {
      Type rawType = ((ParameterizedType) type).getRawType();
      Type[] arguments = ((ParameterizedType) type).getActualTypeArguments();
      StringBuilder builder = new StringBuilder();
      String separator = "";
      for (Type argument : arguments) {
        builder.append(format("%s%s", separator, getSimpleName(argument)));
        separator = ", ";
      }
      return format("%s<%s>", getSimpleName(rawType), builder);
    } else {
      return type.toString();
    }
  }

  public static Option<String> getPublicScalaName(Type type) {
    if (type instanceof Class<?>) {
      if ((((Class<?>) type).getModifiers() & Modifier.PUBLIC) == 0) {
        return Option.none();
      }
      return Option.some(((Class<?>) type).getName().replace('$', '.'));
    } else if (type instanceof ParameterizedType) {
      Type rawType = ((ParameterizedType) type).getRawType();
      Type[] arguments = ((ParameterizedType) type).getActualTypeArguments();
      StringBuilder builder = new StringBuilder();
      String separator = "";
      for (Type argument : arguments) {
        Option<String> argType = getPublicScalaName(argument);
        if (argType.isEmpty()) {
          return Option.none();
        }
        builder.append(format("%s%s", separator, argType.getOrThrow()));
        separator = ", ";
      }
      for (String recur : getPublicScalaName(rawType)) {
        return Option.some(format("%s[%s]", recur.replace('$', '.'), builder.toString().replace('$', '.')));
      }
      return Option.none();
    } else {
      return Option.some(type.toString().replace('$', '.'));
    }
  }
}
