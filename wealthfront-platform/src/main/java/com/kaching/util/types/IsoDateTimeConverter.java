package com.kaching.util.types;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.joda.time.format.ISODateTimeFormat;

import com.google.inject.Singleton;
import com.kaching.platform.converters.NullHandlingConverter;
import com.wealthfront.util.time.DateTimeZones;

@Singleton
public class IsoDateTimeConverter extends NullHandlingConverter<DateTime> {

  private final DateTimeFormatter noTimezoneFormatter = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss");

  @Override
  protected DateTime fromNonNullableString(String representation) {
    if (representation.length() == 25) {
      return ISODateTimeFormat.dateTimeNoMillis().withZone(DateTimeZones.ET).parseDateTime(representation);
    } else if (representation.length() == 19) {
      return noTimezoneFormatter.withZone(DateTimeZones.ET).parseDateTime(representation);
    } else if (representation.length() == 23) {
      return ISODateTimeFormat.dateHourMinuteSecondMillis().withZone(DateTimeZones.ET).parseDateTime(representation);
    } else {
      return ISODateTimeFormat.dateElementParser().withZone(DateTimeZones.ET).parseDateTime(representation);
    }

  }

  @Override
  protected String nonNullableToString(DateTime value) {
    return value.toString(ISODateTimeFormat.dateTimeNoMillis());
  }

}
