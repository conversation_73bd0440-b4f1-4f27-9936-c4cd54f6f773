package com.kaching.util.types;

import static com.wealthfront.util.time.DateTimeZones.ET;
import static java.sql.Types.DATE;

import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;

import org.hibernate.HibernateException;
import org.hibernate.usertype.UserType;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import com.kaching.platform.hibernate.types.AbstractImmutableType;
import com.wealthfront.util.time.DateTimeZones;

/**
 * A Hibernate type for {@link DateTime} holding only the date. This
 * object can be mapped using
 * <p/>
 * <pre>&lt;typedef name="DateTime_ymd"
 *     class="com.kaching.util.types.DateTimeHibernateTypeYmd" /&gt;</pre>
 */
public class DateTimeHibernateTypeYmd extends AbstractImmutableType
    implements UserType {

  private static final int[] SQL_TYPES = {DATE};

  public int[] sqlTypes() {
    return Arrays.copyOf(SQL_TYPES, SQL_TYPES.length);
  }

  public Class<DateTime> returnedClass() {
    return DateTime.class;
  }

  public final DateTime nullSafeGet(ResultSet rs, String[] names, Object owner)
      throws HibernateException, SQLException {
    Date date = rs.getDate(names[0]);
    // deferred call to wasNull
    // http://java.sun.com/j2se/1.5.0/docs/api/java/sql/ResultSet.html#wasNull()
    if (rs.wasNull() || date == null) {
      return null;
    } else {
      return new LocalDate(date.toString(), ET).toDateTimeAtStartOfDay(ET);
    }
  }

  @Override
  public final void nullSafeSet(PreparedStatement st, Object value, int index)
      throws HibernateException, SQLException {
    super.nullSafeSet(st, value, index);
    if (value == null) {
      st.setNull(index, DATE);
    } else {
      DateTime old = (DateTime) value;
      st.setDate(index, Date.valueOf(DateTimeZones.toLocalDate(old, ET).toString()));
    }
  }

}
