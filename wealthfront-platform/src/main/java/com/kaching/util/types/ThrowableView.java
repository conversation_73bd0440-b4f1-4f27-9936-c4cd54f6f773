package com.kaching.util.types;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.kaching.platform.common.Strings;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;

@Entity
@ExposeType(value = { ExposeTo.BACKEND, ExposeTo.TAOS }, namespace = RewriteNamespace.DO_NOT_COPY)
public class ThrowableView {

  @Value String className;
  @Value String simpleClassName;
  @Value String message;
  @Value ThrowableView cause;
  @Value List<ThrowableView> suppressed;
  @Value List<StackTraceElementView> stackTrace;

  ThrowableView() { /* JSON */ }

  public ThrowableView(String className, String simpleClassName, String message, ThrowableView cause,
                       List<ThrowableView> suppressed, List<StackTraceElementView> stackTrace) {
    this.className = className;
    this.simpleClassName = simpleClassName;
    this.message = message;
    this.cause = cause;
    this.suppressed = suppressed;
    this.stackTrace = stackTrace;
  }

  public String getClassName() {
    return className;
  }

  public String getSimpleClassName() {
    return simpleClassName;
  }

  public String getMessage() {
    return message;
  }

  public String getNewMessage() {
    return Strings.format("(%s) %s", simpleClassName, message);
  }

  public ThrowableView getCause() {
    return cause;
  }

  public List<ThrowableView> getSuppressed() {
    return suppressed;
  }

  public List<StackTraceElementView> getStackTrace() {
    return stackTrace;
  }

  public static ThrowableView fromThrowable(Throwable throwable) {
    ThrowableView cause = throwable.getCause() == null ? null : ThrowableView.fromThrowable(throwable.getCause());
    String className = throwable.getClass().getName();
    String simpleClassName = throwable.getClass().getSimpleName();
    String message = throwable.getMessage();
    List<ThrowableView> suppressed = Arrays.stream(throwable.getSuppressed())
        .filter(ex -> ex != throwable)
        .map(ThrowableView::fromThrowable)
        .collect(Collectors.toList());
    List<StackTraceElementView> stackTrace = Arrays.stream(throwable.getStackTrace())
        .map(StackTraceElementView::fromStackTraceElement)
        .collect(Collectors.toList());
    return new ThrowableView(className, simpleClassName, message, cause, suppressed, stackTrace);
  }

  public Exception toException() {
    Exception ex = (cause == null) ? new Exception(getNewMessage()) : new Exception(getNewMessage(), cause.toException());
    return populateException(ex);
  }

  public RuntimeException toRuntimeException() {
    RuntimeException ex = (cause == null) ? new RuntimeException(getNewMessage()) : new RuntimeException(getNewMessage(), cause.toException());
    return populateException(ex);
  }

  private <T extends Exception> T populateException(T exception) {
    suppressed.stream()
        .map(ThrowableView::toException)
        .forEach(exception::addSuppressed);
    exception.setStackTrace(stackTrace.stream()
        .map(StackTraceElementView::getElement)
        .toArray(StackTraceElement[]::new));
    return exception;
  }

}
