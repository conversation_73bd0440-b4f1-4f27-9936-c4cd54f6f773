package com.kaching.util.types;

import com.kaching.platform.converters.Converter;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.platform.hibernate.Id;

public class TypedIdConverter<T extends HibernateEntity> implements Converter<Id<T>> {

  public Id<T> fromString(String representation) {
    return Id.of(Long.parseLong(representation));
  }

  public String toString(Id<T> value) {
    return value.toString();
  }

}
