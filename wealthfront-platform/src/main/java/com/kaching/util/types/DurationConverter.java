package com.kaching.util.types;

import org.joda.time.Duration;

import com.kaching.platform.converters.NullHandlingConverter;

public class DurationConverter extends NullHandlingConverter<Duration> {

  @Override
  protected Duration fromNonNullableString(String representation) {
    return new Duration(representation);
  }

  @Override
  protected String nonNullableToString(Duration value) {
    return value.toString();
  }

}
