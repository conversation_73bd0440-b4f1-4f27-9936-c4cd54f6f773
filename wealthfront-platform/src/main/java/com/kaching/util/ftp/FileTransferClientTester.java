package com.kaching.util.ftp;

import java.util.Map;
import java.util.Set;

import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.OptionVisitor;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;

public class FileTransferClientTester {

  public Result testClient(FileTransferClient fileTransferClient) {
    return testClient(fileTransferClient, Option.none());
  }

  public Result testClient(FileTransferClient fileTransferClient, String path) {
    return testClient(fileTransferClient, Option.some(path));
  }

  private Result testClient(FileTransferClient fileTransferClient, Option<String> maybePath) {
    try {
      Option<Set<String>> directoryContents = maybePath.visit(new OptionVisitor<>() {

        @Override
        public Option<Set<String>> caseNone() {
          return fileTransferClient.listDirectory();
        }

        @Override
        public Option<Set<String>> caseSome(String path) {
          return fileTransferClient.listDirectory(path);
        }

      });
      return directoryContents.visit(new OptionVisitor<Set<String>, Result>() {

        @Override
        public Result caseNone() {
          return Result.failure(ImmutableMap.of("error", "FileTransferClient.listDirectory() returned none"));
        }

        @Override
        public Result caseSome(Set<String> strings) {
          return Result.success();
        }

      });
    } catch (Exception e) {
      return Result.failure(ImmutableMap.of("exception", Throwables.getStackTraceAsString(e)));
    }
  }

  public Result testClient(FtpsClient fileTransferClient) {
    try {
      fileTransferClient.listDirectory(null);
      return Result.success();
    } catch (Exception e) {
      return Result.failure(ImmutableMap.of("exception", Throwables.getStackTraceAsString(e)));
    }
  }
  
  @Entity
  @ExposeType(value = { ExposeTo.BACKEND }, namespace = RewriteNamespace.DO_NOT_COPY)
  public static class Result {
    
    @Value
    private Boolean success;
    
    @Value(optional = true)
    private Map<String, String> errors;

    private Result(Boolean success, Option<Map<String, String>> errors) {
      this.success = success;
      this.errors = errors.getOrNull();
    }
    
    private Result() { /* JSON */}

    public Boolean getSuccess() {
      return success;
    }

    public Option<Map<String, String>> getErrorsOption() {
      return Option.of(errors);
    }

    public Map<String, String> getErrors() {
      return errors;
    }
    
    public static Result success() {
      return new Result(true, Option.none());
    }

    public static Result failure(Map<String, String> errors) {
      return new Result(false, Option.some(errors));
    }  
  
  }

}
