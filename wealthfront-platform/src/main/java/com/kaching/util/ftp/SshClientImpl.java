package com.kaching.util.ftp;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.joda.time.Duration;

import com.google.common.annotations.VisibleForTesting;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.kaching.platform.common.Pair;
import com.kaching.platform.common.logging.Log;
import com.kaching.util.Sleeper;

public class SshClientImpl implements SshClient {

  private static final int SSH_PORT = 22;
  private static final int SESSION_RETRIES = 3;
  private static final Log LOG = Log.getLog(SshClientImpl.class);

  @VisibleForTesting static final String EXEC_TYPE = "exec";
  @VisibleForTesting static final String NO_KEY_CHECKING = "no";
  @VisibleForTesting static final String STRICT_HOST_KEY_CHECKING = "StrictHostKeyChecking";

  private int port;
  private JSch jSch;
  private Sleeper sleeper;

  @VisibleForTesting
  SshClientImpl(JSch jSch, int port, Sleeper sleeper) {
    this.jSch = jSch;
    this.port = port;
    this.sleeper = sleeper;
  }

  @SuppressWarnings("unused")
  public static SshClientImpl sshClientImpl(JSch jSch, Sleeper sleeper) {
    return new SshClientImpl(jSch, SSH_PORT, sleeper);
  }

  @Override
  public Pair<Integer, List<String>> executeAndGetExitStatus(String username, String host, String command) {
    LOG.info("Running command ( %s ) on host ( %s )", command, host);
    Session session = null;
    //noinspection ConstantConditions
    for (int i = 0; i < SESSION_RETRIES; ++i) {
      try {
        session = jSch.getSession(username, host, port);
        session.setConfig(STRICT_HOST_KEY_CHECKING, NO_KEY_CHECKING);
        session.connect();
        break;
      } catch (JSchException e) {
        if (i + 1 == SESSION_RETRIES) {
          LOG.error(e,
              "Error connecting session to ( %s ) as ( %s ) using port ( %s ) with command ( %s ). Returning empty list",
              host, username, port, command, i);
          return Pair.of(null, Collections.emptyList());
        }
        LOG.error(e,
            "Error connecting session to ( %s ) as ( %s ) using port ( %s ) with command ( %s ). Retried ( %s ) times",
            host, username, port, command, i);
      }
    }
    List<String> lines = List.of();
    ChannelExec channelExec = null;
    Integer exitStatus = null;
    try {
      channelExec = (ChannelExec) session.openChannel(EXEC_TYPE);
      channelExec.setCommand(command);

      InputStream inputStream = channelExec.getInputStream();
      channelExec.connect();
      LOG.info("Session & channel connected to run command ( %s )", command);

      lines = readAllInputLines(inputStream, channelExec);
      LOG.info("Read ( %s ) lines for command ( %s )", lines.size(), command);

      exitStatus = channelExec.getExitStatus();
      LOG.info("Exited with status ( %s ) for command ( %s )", exitStatus, command);

    } catch (JSchException e) {
      LOG.error(e,
          "Error connecting channel to ( %s ) as ( %s ) using port ( %s ) with command ( %s ). Returning empty list",
          host, username, port, command);
      return Pair.of(exitStatus, Collections.emptyList());
    } catch (IOException e) {
      LOG.error(e, "error reading output of command ( %s ) in ( %s ) as ( %s )", command, host, username);
    } finally {
      if (channelExec != null) {
        channelExec.disconnect();
      }
      session.disconnect();
      LOG.info("Disconnected from ( %s ) after running command ( %s )", host, command);
    }
    return Pair.of(exitStatus, lines);
  }

  @Override
  public List<String> executeCommand(String username, String host, String command) {
    return executeAndGetExitStatus(username, host, command).getRight();
  }

  private List<String> readAllInputLines(InputStream in, ChannelExec channelExec) throws IOException {
    ByteArrayOutputStream outputBuffer = new ByteArrayOutputStream();
    byte[] buffer = new byte[1024];

    while (true) {
      while (in.available() > 0) {
        int i = in.read(buffer, 0, buffer.length);
        if (i < 0) {
          break;
        }
        outputBuffer.write(buffer, 0, i);
      }
      if (channelExec.isClosed()) {
        if (in.available() > 0) {
          continue;
        }
        break;
      }

      sleeper.sleep(Duration.standardSeconds(1));
    }

    return Arrays.asList(outputBuffer.toString().split("\n"));
  }

}
