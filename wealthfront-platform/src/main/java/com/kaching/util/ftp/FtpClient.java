package com.kaching.util.ftp;

import static com.google.common.io.ByteStreams.copy;
import static com.kaching.platform.common.logging.Log.getLog;
import static java.lang.String.format;
import static org.apache.commons.net.ftp.FTPReply.isPositiveCompletion;

import java.io.ByteArrayInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;

import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.joda.time.DateTime;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.io.ByteStreams;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.util.io.UncheckedIo;

public class FtpClient implements FileTransferClient {

  private static final Log log = getLog(FtpClient.class);

  private final String host;
  private final String username;
  private final String password;
  private Option<Integer> maybePort = Option.none();
  private Option<Integer> maybeTimeout = Option.none();

  public FtpClient(String host, String username, String password) {
    this(host, username, password, Option.none());
  }

  public FtpClient(String host, String username, String password, Option<Integer> timeout) {
    this.host = host;
    this.username = username;
    this.password = password;
    this.maybeTimeout = timeout;
  }

  public enum FtpFileType {
    ASCII(FTP.ASCII_FILE_TYPE),
    BINARY(FTP.BINARY_FILE_TYPE),
    EBCDIC(FTP.EBCDIC_FILE_TYPE),
    LOCAL(FTP.LOCAL_FILE_TYPE);

    private final int apacheCommonsFTPFileType;

    FtpFileType(int apacheCommonsFTPFileType) {
      this.apacheCommonsFTPFileType = apacheCommonsFTPFileType;
    }

    int getApacheCommonsFTPFileType() {
      return apacheCommonsFTPFileType;
    }
  }

  public <T> T connect(WithLoggedInClientExpression<T> withLoggedInClient) throws IOException {
    FTPClient client = null;
    boolean loggedIn = false;
    try {
      client = new FTPClient();
      for (Integer timeout : maybeTimeout) {
        client.setConnectTimeout(timeout);
        client.setDataTimeout(timeout);
      }
      if (maybePort.isDefined()) {
        client.connect(host, maybePort.getOrThrow());
      } else {
        client.connect(host);
      }
      if (!isPositiveCompletion(client.getReplyCode())) {
        throw new RuntimeException("could not connect to the FTP server");
      }
      if (!client.login(username, password)) {
        throw new RuntimeException("could not log in to the FTP server");
      }
      loggedIn = true;
      return withLoggedInClient.apply(client);
    } finally {
      if (client != null && client.isConnected()) {
        if (loggedIn) {
          client.logout();
        }
        client.disconnect();
      }
    }
  }

  @Override
  public Option<Set<String>> listDirectory(String path) {
    return doListDirectory(Option.of(path), false);
  }

  @Override
  public Set<String> listDirectoryOrThrow(String path) {
    return doListDirectory(Option.of(path), true).getOrThrow();
  }

  @Override
  public Option<Set<String>> listDirectory() {
    return doListDirectory(Option.none(), false);
  }

  @Override
  public Set<String> listDirectoryOrThrow() {
    return doListDirectory(Option.none(), true).getOrThrow();
  }

  @Override
  public Option<Map<String, DateTime>> listDirectoryWithMTime(String path) {
    throw new UnsupportedOperationException("listDirectoryWithMTime not yet impl for FtpClient");
  }

  private Option<Set<String>> doListDirectory(final Option<String> pathname, boolean propagateExceptions) {
    try {
      Set<String> result = connect(new WithLoggedInClientExpression<Set<String>>() {
        @Override
        public Set<String> apply(FTPClient client) throws IOException {
          client.enterLocalPassiveMode();
          FTPFile[] names = pathname.isDefined() ? client.listFiles(pathname.getOrThrow()) : client.listFiles();

          HashSet<String> result = new HashSet<>();
          for (FTPFile file : names) {
            result.add(file.getName());
          }
          return result;
        }
      });
      return Option.of(result);
    } catch (Exception e) {
      if (propagateExceptions) {
        throw new RuntimeException(e);
      }
      log.error(e);
      return Option.none();
    }
  }

  @Override
  public Option<byte[]> downloadBytes(String path) {
    try {
      for (InputStream is : download(path)) {
        byte[] returnValue = ByteStreams.toByteArray(is);
        is.close();
        return Option.of(returnValue);
      }
      return Option.none();
    } catch (IOException e) {
      log.error(e);
      throw new RuntimeException(e);
    }
  }

  @Override
  public byte[] downloadBytesOrThrow(String path) {
    try (InputStream is = downloadOrThrow(path)) {
      return ByteStreams.toByteArray(is);
    } catch (Exception e) {
      throw new RuntimeException("Failed to download file at path " + path, e);
    }
  }

  public Option<InputStream> download(String path) {
    try {
      return Option.some(downloadOrThrow(path));
    } catch (FileNotFoundException e) {
      log.error(e, "can't find file %s", path);
      return Option.none();
    } catch (IOException e) {
      log.error(e);
      throw new RuntimeException(e);
    }
  }

  public InputStream downloadOrThrow(String path) throws IOException {
    return openConnection(path).getInputStream();
  }

  public boolean download(
      final String path, final FtpFileType fileType, final OutputStream outStream) throws IOException {
    return connect(new WithLoggedInClientExpression<Boolean>() {
      @Override
      public Boolean apply(FTPClient client) throws IOException {
        client.enterLocalPassiveMode();
        client.setFileType(fileType.getApacheCommonsFTPFileType());
        client.setBufferSize(1024 * 1024);
        client.setControlKeepAliveTimeout(300);
        return client.retrieveFile(path, outStream);
      }
    });
  }

  @Override
  public boolean download(String path, OutputStream outputStream) {
    UncheckedIo.run(() -> downloadOrThrow(path, outputStream));
    return true;
  }

  @Override
  public void downloadOrThrow(String path, OutputStream outputStream) {
    try {
      download(path, FtpFileType.BINARY, outputStream);
    } catch (Exception e) {
      throw new RuntimeException("Failed to download file at path " + path, e);
    }
  }

  @Override
  public boolean download(String path, OutputStream outputStream, boolean shouldDiscover) {
    if (shouldDiscover) {
      throw new UnsupportedOperationException("download with discover unimplemented in FtpClient");
    } else {
      return download(path, outputStream);
    }
  }

  @Override
  public void downloadOrThrow(String path, OutputStream outputStream, boolean shouldDiscover) {
    if (shouldDiscover) {
      throw new UnsupportedOperationException("download with discover unimplemented in FtpClient");
    } else {
      downloadOrThrow(path, outputStream);
    }
  }

  @Override
  public boolean downloadToFile(String downloadFromPath, String outputPath) {
    return downloadToFile(downloadFromPath, Paths.get(outputPath));
  }

  @Override
  public void downloadToFileOrThrow(String path, String outputPath) {
    downloadToFile(path, Paths.get(outputPath));
  }

  public boolean downloadToFile(String downloadFromPath, Path outputPath) {
    log.info("downloading %s", downloadFromPath);
    Path tmpDownloadFromPath = outputPath.resolveSibling(outputPath.getFileName() + ".tmp");
    return UncheckedIo.get(() -> {
      try (OutputStream outputStream = Files.newOutputStream(tmpDownloadFromPath)) {
        download(downloadFromPath, FtpFileType.BINARY, outputStream);
      }
      Files.move(tmpDownloadFromPath, outputPath);
      log.info("downloaded %s to %s", downloadFromPath, outputPath);
      return true;
    });
  }

  @Override
  public List<String> downloadRecursively(String path, String outputDir) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<String> downloadNewFilesRecursively(String path, String outputDir, Option<String> filePathsToSkip) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<String> downloadNewFilesRecursively(String path, String outputDir, List<String> filePathsToSkip) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<String> downloadNewFiles(String path, String outputDir, Option<String> fileNameFilter) {
    throw new UnsupportedOperationException();
  }

  @Override
  public List<String> downloadNewFilesWithPredicate(String path, String outputDir,
                                                    Predicate<String> predicate) {
    throw new UnsupportedOperationException("downloadNewFilesWithPredicate is only supported for SftpClient");
  }

  @Override
  public OutputStream upload(String path) throws IOException {
    return openConnection(path).getOutputStream();
  }

  @Override
  public boolean delete(String path) throws IOException {
    return connect(client -> {
      client.enterLocalPassiveMode();
      return client.deleteFile(path);
    });
  }

  @Override
  public OutputStream upload(final String path, final FtpFileType fileType) throws IOException {
    return connect(client -> {
      client.enterLocalPassiveMode();
      client.setFileType(fileType.getApacheCommonsFTPFileType());
      return client.storeFileStream(path);
    });
  }

  @Override
  public void upload(String path, byte[] data) {
    try {
      OutputStream out = upload(path);
      copy(new ByteArrayInputStream(data), out);
      out.close();
    } catch (IOException e) {
      log.error(e);
      throw new RuntimeException(e);
    }
  }

  @VisibleForTesting
  URI getUri(String path) {
    try {
      return new URI(format("ftp://%s:%s@%s/%s", username, password, host, path));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  @VisibleForTesting
  URLConnection openConnection(String path) throws IOException {
    try {
      return getUri(path).toURL().openConnection();
    } catch (MalformedURLException e) {
      throw new RuntimeException(e);
    }
  }

  @VisibleForTesting
  void setPort(int port) {
    this.maybePort = Option.some(port);
  }

  public interface WithLoggedInClientExpression<T> {

    T apply(FTPClient client) throws IOException;

  }

}
