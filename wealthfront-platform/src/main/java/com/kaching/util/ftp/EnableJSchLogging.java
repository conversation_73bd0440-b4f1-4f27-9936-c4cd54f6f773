package com.kaching.util.ftp;

import static com.kaching.platform.common.logging.Log.getLog;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Logger;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.AbstractQuery;

public class EnableJSchLogging extends AbstractQuery<Boolean> {

  private static final Log LOG = getLog(JSch.class);

  @Override
  public Boolean process() {
    JSch.setLogger(new Logger() {
      @Override
      public boolean isEnabled(int level) {
        switch (level) {
          case Logger.DEBUG:
            return LOG.isDebugEnabled();
          case Logger.INFO:
          case Logger.WARN:
          case Logger.ERROR:
          case Logger.FATAL:
            return true;
          default:
            return false;
        }
      }

      @Override
      public void log(int level, String message) {
        switch (level) {
          case Logger.DEBUG:
            LOG.debug(message);
            break;
          case Logger.INFO:
            LOG.info(message);
            break;
          case Logger.WARN:
            LOG.warn(message);
            break;
          case Logger.ERROR:
          case Logger.FATAL:
          default:
            LOG.error(message);
            break;
        }
      }
    });

    return true;
  }

}
