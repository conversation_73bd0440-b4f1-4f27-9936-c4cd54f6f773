package com.kaching.util.ftp;

import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;

import org.joda.time.DateTime;

import com.kaching.platform.common.Option;

public interface FileTransferClient {

  Option<Set<String>> listDirectory(String path);

  Set<String> listDirectoryOrThrow(String path);

  Option<Set<String>> listDirectory();

  Set<String> listDirectoryOrThrow();

  Option<Map<String, DateTime>> listDirectoryWithMTime(String path);

  Option<byte[]> downloadBytes(String path);

  byte[] downloadBytesOrThrow(String path);

  boolean download(String path, OutputStream outputStream);

  void downloadOrThrow(String path, OutputStream outputStream);

  boolean download(String path, OutputStream outputStream, boolean shouldDiscover);

  void downloadOrThrow(String path, OutputStream outputStream, boolean shouldDiscover);

  boolean downloadToFile(String path, String outputPath);

  void downloadToFileOrThrow(String path, String outputPath);

  List<String> downloadRecursively(String path, String outputDir);

  List<String> downloadNewFilesRecursively(String path, String outputDir, Option<String> fileNameFilter);

  List<String> downloadNewFilesRecursively(String path, String outputDir, List<String> filePathsToSkip);

  List<String> downloadNewFiles(String path, String outputDir, Option<String> fileNameFilter);

  List<String> downloadNewFilesWithPredicate(String path, String outputDir, Predicate<String> predicate);

  OutputStream upload(String path) throws IOException;

  boolean delete(String path) throws IOException;

  OutputStream upload(String path, FtpClient.FtpFileType fileType) throws IOException;

  void upload(String path, byte[] data);

}
