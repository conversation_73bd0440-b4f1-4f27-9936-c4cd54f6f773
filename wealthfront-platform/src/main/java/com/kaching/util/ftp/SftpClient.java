package com.kaching.util.ftp;

import static com.google.common.io.ByteStreams.toByteArray;
import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.util.io.Closeables.closeQuietly;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static java.lang.String.format;
import static java.util.stream.Collectors.toMap;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.Vector;
import java.util.function.Function;
import java.util.function.Predicate;

import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.perf4j.StopWatch;

import com.amazonaws.util.IOUtils;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableList;
import com.google.common.io.ByteSource;
import com.google.common.io.Files;
import com.google.inject.Provider;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.jcraft.jsch.UserInfo;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.util.functional.Pointer;

public class SftpClient implements FileTransferClient {

  private static final Log log = getLog(SftpClient.class);
  @VisibleForTesting static final int DEFAULT_SFTP_PORT = 22;

  private final String host;
  private final int port;
  private final String username;
  private final String password;
  private final StackTraceMonitor monitor;
  private final JSch jsch;
  private final Provider<StopWatch> stopWatchProvider;
  private final Properties config;
  private final Option<UserInfo> maybeUserInfo;
  private Option<Duration> timeout = Option.none();

  public SftpClient(String host, String username, String password, JSch jsch, StackTraceMonitor monitor,
                    Provider<StopWatch> stopWatchProvider) {
    this(host, username, password, jsch, monitor, stopWatchProvider, new Properties());
  }

  public SftpClient(String host, String username, String password, JSch jsch, StackTraceMonitor monitor,
                    Provider<StopWatch> stopWatchProvider, Properties config) {
    this(host, DEFAULT_SFTP_PORT, username, password, jsch, monitor, stopWatchProvider, config);
  }

  public SftpClient(
      String host, String username, String password, JSch jsch, StackTraceMonitor monitor,
      Provider<StopWatch> stopWatchProvider, Option<UserInfo> maybeUserInfo) {
    this(host, DEFAULT_SFTP_PORT, username, password, jsch, monitor, stopWatchProvider, new Properties(),
        maybeUserInfo);
  }

  public SftpClient(String host, int port, String username, String password, JSch jsch, StackTraceMonitor monitor,
                    Provider<StopWatch> stopWatchProvider, Properties config) {
    this(host, port, username, password, jsch, monitor, stopWatchProvider, config, Option.none());
  }

  public SftpClient(String host, int port, String username, String password, JSch jsch, StackTraceMonitor monitor,
                    Provider<StopWatch> stopWatchProvider, Properties config, Option<UserInfo> maybeUserInfo) {
    this.host = host;
    this.port = port;
    this.username = username;
    this.password = password;
    this.jsch = jsch;
    this.monitor = monitor;
    this.stopWatchProvider = stopWatchProvider;
    this.config = config;
    this.maybeUserInfo = maybeUserInfo;
  }
  
  public void setTimeout(Option<Duration> timeout) {
    this.timeout = timeout;
  }

  @Override
  public Option<Set<String>> listDirectory(String path) {
    return doListDirectory(Option.of(path), false);
  }
  
  @Override
  public Set<String> listDirectoryOrThrow(String path) {
    return doListDirectory(Option.of(path), true).getOrThrow();
  }

  @Override
  public Option<Set<String>> listDirectory() {
    return doListDirectory(Option.none(), false);
  }

  @Override
  public Set<String> listDirectoryOrThrow() {
    return doListDirectory(Option.none(), true).getOrThrow();
  }

  /*
   * Modified-at time (unix mtime) returned is precise to the second, NOT millisecond, due to Jsch
   * http://epaul.github.io/jsch-documentation/javadoc/com/jcraft/jsch/SftpATTRS.html#getMTime()
   *
   * Note that with Java 8, the java.nio.file package supports getting mtime with millisecond precision
   */
  @Override
  public Option<Map<String, DateTime>> listDirectoryWithMTime(String path) {
    return doListDirectoryFullEntries(Option.some(path), false).transform(map -> map.entrySet().stream().collect(toMap(
        Map.Entry::getKey,
        entry -> new DateTime(entry.getValue().getAttrs().getMTime() * 1000L, ET)
    )));
  }

  private Option<Set<String>> doListDirectory(Option<String> pathOpt, boolean propagateExceptions) {
    return doListDirectoryFullEntries(pathOpt, propagateExceptions).transform(Map::keySet);
  }

  private Option<Map<String, LsEntry>> doListDirectoryFullEntries(Option<String> pathOpt, boolean propagateExceptions) {
    Session session = null;
    ChannelSftp channel = null;
    try {
      session = jsch.getSession(username, host, port);
      session.setPassword(password);
      session.setConfig(config);
      maybeUserInfo.ifDefined(session::setUserInfo);
      maybeSetTimeout(session);
      session.connect();

      channel = (ChannelSftp) session.openChannel("sftp");
      channel.connect();

      String path = pathOpt.isDefined() ? pathOpt.getOrThrow() : channel.pwd();
      @SuppressWarnings("unchecked")
      Vector<LsEntry> entries = channel.ls(path);
      return Option.of(entries.stream()
          .filter(entry -> !ImmutableList.of("..", ".").contains(entry.getFilename()))
          .collect(toMap(
              entry -> Joiner.on("/").join(path.replaceAll("/$", ""), entry.getFilename()),
              Function.identity())));
    } catch (JSchException | SftpException e) {
      if (propagateExceptions) {
        throw new RuntimeException(e);
      }
      log.error(e);
      monitor.add(e);
      return Option.none();
    } finally {
      if (channel != null) {
        channel.disconnect();
      }
      if (session != null) {
        session.disconnect();
      }
    }
  }

  @Override
  public Option<byte[]> downloadBytes(String path) {
    try {
      return Option.some(downloadBytesOrThrow(path));
    } catch (Exception e) {
      monitor.add(new RuntimeException("Exception while downloading " + path, e));
      return Option.none();
    }
  }

  @Override
  public byte[] downloadBytesOrThrow(String path) {
    Session session = null;
    ChannelSftp channel = null;
    InputStream in = null;
    try {
      session = jsch.getSession(username, host, port);
      session.setPassword(password);
      session.setConfig(config);
      maybeUserInfo.ifDefined(session::setUserInfo);

      maybeSetTimeout(session);

      session.connect();

      channel = (ChannelSftp) session.openChannel("sftp");
      channel.connect();

      // TODO (julien) detect if the file doesn't exist

      in = channel.get(path);
      return toByteArray(in);
    } catch (Exception e) {
      throw new RuntimeException("Failed to download file at path " + path, e);
    } finally {
      closeQuietly(in);
      if (channel != null) {
        channel.disconnect();
      }
      if (session != null) {
        session.disconnect();
      }
    }
  }

  @Override
  public boolean download(String path, final OutputStream outputStream) {
    return download(path, outputStream, false);
  }

  @Override
  public void downloadOrThrow(String path, OutputStream outputStream) {
    downloadOrThrow(path, outputStream, false);
  }

  @Override
  public boolean download(String path, final OutputStream outputStream, boolean shouldDiscover) {
    try {
      downloadOrThrow(path, outputStream, shouldDiscover);
      return true;
    } catch (RuntimeException e) {
      monitor.add(new RuntimeException("Failed to download file. Returning false.", e));
      return false;
    }
  }

  @Override
  public void downloadOrThrow(String path, final OutputStream outputStream, boolean shouldDiscover) {
    Session session = null;
    ChannelSftp channel = null;
    InputStream in = null;
    try {
      session = jsch.getSession(username, host, port);
      session.setPassword(password);
      session.setConfig(config);
      maybeUserInfo.ifDefined(session::setUserInfo);
      maybeSetTimeout(session);
      session.connect();

      channel = (ChannelSftp) session.openChannel("sftp");
      channel.connect();

      if (shouldDiscover) {
        discover(channel, path);
      }

      in = channel.get(path);
      IOUtils.copy(in, outputStream);
    } catch (Exception e) {
      throw new RuntimeException("Failed to download file at path " + path, e);
    } finally {
      closeQuietly(in);
      if (channel != null) {
        channel.disconnect();
      }
      if (session != null) {
        session.disconnect();
      }
    }
  }

  @VisibleForTesting
  void discover(ChannelSftp channel, String path) throws SftpException {
    Joiner joiner = Joiner.on("/");
    String[] pathSplit = path.split("/");
    String parentPath = pathSplit[0];
    if (parentPath.length() > 0) {
      channel.ls(parentPath);
    }
    if (pathSplit.length > 1) {
      for (String pathPart : Arrays.copyOfRange(pathSplit, 1, pathSplit.length)) {
        parentPath = joiner.join(parentPath, pathPart);
        channel.ls(parentPath);
      }
    }
  }

  @Override
  public boolean downloadToFile(String path, String outputPath) {
    try {
      downloadToFileOrThrow(path, outputPath);
      return true;
    } catch (RuntimeException e) {
      if (!e.getCause().getMessage().contains("No such file")) {
        monitor.add(new RuntimeException("Failed to download file. Returning false.", e));
      }
      return false;
    }
  }

  @Override
  public void downloadToFileOrThrow(String path, String outputPath) {
    Session session = null;
    ChannelSftp channel = null;
    StopWatch watch = stopWatchProvider.get();
    try {
      session = jsch.getSession(username, host, port);
      session.setPassword(password);
      session.setConfig(config);
      maybeUserInfo.ifDefined(session::setUserInfo);
      maybeSetTimeout(session);
      session.connect();

      channel = (ChannelSftp) session.openChannel("sftp");
      channel.connect();

      watch.start();
      channel.get(path, outputPath);
      log.info(watch.lap("download-to-file",
          format("finished download from path %s to output path %s", path, outputPath)));
    } catch (Exception e) {
      throw new RuntimeException("Failed to download file at path " + path, e);
    } finally {
      if (channel != null) {
        channel.disconnect();
      }
      if (session != null) {
        session.disconnect();
      }
    }
  }

  @Override
  public List<String> downloadRecursively(String path, String outputDir) {
    Session session = null;
    ChannelSftp channel = null;
    try {
      session = jsch.getSession(username, host, port);
      session.setPassword(password);
      session.setConfig(config);
      maybeUserInfo.ifDefined(session::setUserInfo);
      maybeSetTimeout(session);
      session.connect();

      channel = (ChannelSftp) session.openChannel("sftp");
      channel.connect();

      return downloadRecursively(channel, path, outputDir, false, Option.none());
    } catch (Exception e) {
      throw new RuntimeException("Failed to recursively download at path " + path, e);
    } finally {
      if (channel != null) {
        channel.disconnect();
      }
      if (session != null) {
        session.disconnect();
      }
    }
  }

  @Override
  public List<String> downloadNewFilesRecursively(String path, String outputDir, Option<String> fileNameFilter) {
    Session session = null;
    ChannelSftp channel = null;
    try {
      session = jsch.getSession(username, host, port);
      session.setPassword(password);
      session.setConfig(config);
      maybeUserInfo.ifDefined(session::setUserInfo);
      maybeSetTimeout(session);
      session.connect();

      channel = (ChannelSftp) session.openChannel("sftp");
      channel.connect();

      return downloadRecursively(channel, path, outputDir, true, fileNameFilter);
    } catch (IOException | JSchException | SftpException e) {
      throw new RuntimeException(e);
    } finally {
      if (channel != null) {
        channel.disconnect();
      }
      if (session != null) {
        session.disconnect();
      }
    }
  }

  @Override
  public List<String> downloadNewFilesRecursively(String path, String outputDir, List<String> filePathsToSkip) {
    Session session = null;
    ChannelSftp channel = null;
    try {
      session = jsch.getSession(username, host, port);
      session.setPassword(password);
      session.setConfig(config);
      maybeUserInfo.ifDefined(session::setUserInfo);
      maybeSetTimeout(session);
      session.connect();

      channel = (ChannelSftp) session.openChannel("sftp");
      channel.connect();

      return downloadRecursively(channel, path, outputDir, filePathsToSkip);
    } catch (IOException | JSchException | SftpException e) {
      throw new RuntimeException(e);
    } finally {
      if (channel != null) {
        channel.disconnect();
      }
      if (session != null) {
        session.disconnect();
      }
    }
  }

  @Override
  public List<String> downloadNewFiles(String path, String outputDir, Option<String> fileNameFilter) {
    Session session = null;
    ChannelSftp channel = null;
    try {
      session = jsch.getSession(username, host, port);
      session.setPassword(password);
      session.setConfig(config);
      maybeUserInfo.ifDefined(session::setUserInfo);
      maybeSetTimeout(session);
      session.connect();

      channel = (ChannelSftp) session.openChannel("sftp");
      channel.connect();

      return download(channel, path, outputDir, true, fileNameFilter);
    } catch (IOException | JSchException | SftpException e) {
      throw new RuntimeException(e);
    } finally {
      if (channel != null) {
        channel.disconnect();
      }
      if (session != null) {
        session.disconnect();
      }
    }
  }

  private List<String> download(
      ChannelSftp channel, String path, String outputDir,
      boolean checkIfFilesArePresentLocally, Option<String> fileNameFilter) throws SftpException, IOException {
    List<String> downloaded = new ArrayList<>();
    Joiner joiner = Joiner.on("/");
    @SuppressWarnings("unchecked")
    Vector<LsEntry> entries = channel.ls(path);
    int skipCount = 0;
    for (LsEntry entry : entries) {
      final String entryFilename = entry.getFilename();
      if (!ImmutableList.of("..", ".").contains(entryFilename)) {
        String entryPath = joiner.join(path, entryFilename);
        final String newFilePath = joiner.join(outputDir, entryFilename);
        File output = getNewFile(newFilePath);
        if (!entry.getAttrs().isDir()) {
          boolean nameMatch = fileNameFilter.isEmpty() || entryFilename.contains(fileNameFilter.getOrThrow());
          if ((!checkIfFilesArePresentLocally || !output.exists()) && nameMatch) {
            StopWatch watch = stopWatchProvider.get();
            output.createNewFile();
            watch.start();
            copyRemoteContentToFile(channel, entryPath, output);
            log.info(watch.lap("download-recursively",
                format("finished download from path %s to output path %s", entryPath, outputDir)));
            downloaded.add(output.getCanonicalPath());
          } else {
            skipCount += 1;
          }
        }
      }
    }
    log.info("Downloaded %s files from %s. Skipped %s files", downloaded.size(), path, skipCount);
    return downloaded;
  }

  public List<String> downloadNewFilesWithPredicate(String path, String outputDir, Predicate<String> predicate) {
    Session session = null;
    ChannelSftp channel = null;
    try {
      session = jsch.getSession(username, host, port);
      session.setPassword(password);
      session.setConfig(config);
      maybeUserInfo.ifDefined(session::setUserInfo);
      maybeSetTimeout(session);
      session.connect();

      channel = (ChannelSftp) session.openChannel("sftp");
      channel.connect();

      return downloadWithPredicate(channel, path, outputDir, predicate);
    } catch (IOException | JSchException | SftpException e) {
      throw new RuntimeException(e);
    } finally {
      if (channel != null) {
        channel.disconnect();
      }
      if (session != null) {
        session.disconnect();
      }
    }
  }

  public List<String> downloadWithPredicate(
      ChannelSftp channel, String path, String outputDir,
      Predicate<String> predicate) throws SftpException, IOException {
    List<String> downloaded = new ArrayList<>();
    Joiner joiner = Joiner.on("/");
    @SuppressWarnings("unchecked")
    Vector<LsEntry> entries = channel.ls(path);
    int skipCount = 0;
    for (LsEntry entry : entries) {
      final String entryFilename = entry.getFilename();
      if (!ImmutableList.of("..", ".").contains(entryFilename)) {
        String entryPath = joiner.join(path, entryFilename);
        final String newFilePath = joiner.join(outputDir, entryFilename);
        File output = getNewFile(newFilePath);
        if (!entry.getAttrs().isDir()) {
          if ((!output.exists()) && predicate.test(entryFilename)) {
            StopWatch watch = stopWatchProvider.get();
            output.createNewFile();
            watch.start();
            copyRemoteContentToFile(channel, entryPath, output);
            log.info(watch.lap("download-recursively",
                format("finished download from path %s to output path %s", entryPath, outputDir)));
            downloaded.add(output.getCanonicalPath());
          } else {
            skipCount += 1;
          }
        }
      }
    }
    log.info("Downloaded %s files from %s. Skipped %s files", downloaded.size(), path, skipCount);
    return downloaded;
  }

  private List<String> downloadRecursively(
      ChannelSftp channel, String path, String outputDir,
      boolean checkIfFilesArePresentLocally, Option<String> fileNameFilter) throws SftpException, IOException {
    List<String> downloaded = new ArrayList<>();
    Joiner joiner = Joiner.on("/");
    @SuppressWarnings("unchecked")
    Vector<LsEntry> entries = channel.ls(path);
    int skipCount = 0;
    for (LsEntry entry : entries) {
      final String entryFilename = entry.getFilename();
      if (!ImmutableList.of("..", ".").contains(entryFilename)) {
        String entryPath = joiner.join(path, entryFilename);
        File output = getNewFile(joiner.join(outputDir, entryPath));
        if (entry.getAttrs().isDir()) {
          output.mkdirs();
          downloaded.addAll(downloadRecursively(channel, entryPath, outputDir, checkIfFilesArePresentLocally,
              fileNameFilter));
        } else {
          boolean nameMatch = fileNameFilter.isEmpty() || entryFilename.contains(fileNameFilter.getOrThrow());
          if ((!checkIfFilesArePresentLocally || !output.exists()) && nameMatch) {
            StopWatch watch = stopWatchProvider.get();
            output.createNewFile();
            watch.start();
            copyRemoteContentToFile(channel, entryPath, output);
            log.info(watch.lap("download-recursively",
                format("finished download from path %s to output path %s", entryPath, outputDir)));
            downloaded.add(output.getCanonicalPath());
          } else {
            skipCount += 1;
          }
        }
      }
    }
    log.info("Downloaded %s files from %s. Skipped %s files", downloaded.size(), path, skipCount);
    return downloaded;
  }

  private List<String> downloadRecursively(
      ChannelSftp channel, String path, String outputDir, List<String> filePathsToSkip) throws SftpException, IOException {
    List<String> downloaded = new ArrayList<>();
    Joiner joiner = Joiner.on("/");
    @SuppressWarnings("unchecked")
    Vector<LsEntry> entries = channel.ls(path);
    int skipCount = 0;
    for (LsEntry entry : entries) {
      final String entryFilename = entry.getFilename();
      if (!ImmutableList.of("..", ".").contains(entryFilename)) {
        String entryPath = joiner.join(path, entryFilename);
        File output = getNewFile(joiner.join(outputDir, entryPath));
        if (entry.getAttrs().isDir()) {
          output.mkdirs();
          downloaded.addAll(downloadRecursively(channel, entryPath, outputDir, filePathsToSkip));
        } else {
          boolean nameMatch = filePathsToSkip.contains(entryPath);
          if (!output.exists() && !nameMatch) {
            StopWatch watch = stopWatchProvider.get();
            output.createNewFile();
            watch.start();
            copyRemoteContentToFile(channel, entryPath, output);
            log.info(watch.lap("download-recursively",
                format("finished download from path %s to output path %s", entryPath, outputDir)));
            downloaded.add(output.getCanonicalPath());
          } else {
            skipCount += 1;
          }
        }
      }
    }
    log.info("Downloaded %s files from %s. Skipped %s files", downloaded.size(), path, skipCount);
    return downloaded;
  }

  @VisibleForTesting
  File getNewFile(String path) {
    return new File(path);
  }

  @VisibleForTesting
  void copyRemoteContentToFile(ChannelSftp channel, String entryPath, File output) throws SftpException, IOException {
    final InputStream stream = channel.get(entryPath);
    new ByteSource() {
      @Override
      public InputStream openStream() {
        return stream;
      }
    }.copyTo(Files.asByteSink(output));
  }

  @Override
  public OutputStream upload(String path) {
    Pointer<Session> session = Pointer.pointer();
    Pointer<ChannelSftp> channel = Pointer.pointer();
    try {
      session.set(jsch.getSession(username, host, port));
      session.get().setPassword(password);
      session.get().setConfig(config);
      maybeUserInfo.ifDefined(session.get()::setUserInfo);
      maybeSetTimeout(session.get());
      session.get().connect();

      channel.set((ChannelSftp) session.get().openChannel("sftp"));
      channel.get().connect();

      OutputStream os = channel.get().put(path);
      return new OutputStream() {
        @Override
        public void write(int b) throws IOException {
          os.write(b);
        }

        @Override
        public void close() throws IOException {
          os.close();
          if (!channel.isEmpty()) {
            channel.get().disconnect();
          }
          if (!session.isEmpty()) {
            session.get().disconnect();
          }
        }

        @Override
        public void write(byte[] b) throws IOException {
          os.write(b);
        }

        @Override
        public void write(byte[] b, int off, int len) throws IOException {
          os.write(b, off, len);
        }

        @Override
        public void flush() throws IOException {
          os.flush();
        }

      };

    } catch (RuntimeException e) {
      if (!channel.isEmpty()) {
        channel.get().disconnect();
      }
      if (!session.isEmpty()) {
        session.get().disconnect();
      }
      throw e;
    } catch (Exception e) {
      if (!channel.isEmpty()) {
        channel.get().disconnect();
      }
      if (!session.isEmpty()) {
        session.get().disconnect();
      }
      monitor.add(e);
      return null;
    }
  }

  @Override
  public boolean delete(String path) throws IOException {
    Pointer<Session> session = Pointer.pointer();
    Pointer<ChannelSftp> channel = Pointer.pointer();
    try {
      session.set(jsch.getSession(username, host, port));
      session.get().setPassword(password);
      session.get().setConfig(config);
      maybeUserInfo.ifDefined(session.get()::setUserInfo);
      maybeSetTimeout(session.get());
      session.get().connect();

      channel.set((ChannelSftp) session.get().openChannel("sftp"));
      channel.get().connect();

      channel.get().rm(path);
      if (!channel.isEmpty()) {
        channel.get().disconnect();
      }
      if (!session.isEmpty()) {
        session.get().disconnect();
      }
      return true;
    } catch (RuntimeException e) {
      if (!channel.isEmpty()) {
        channel.get().disconnect();
      }
      if (!session.isEmpty()) {
        session.get().disconnect();
      }
      throw e;
    } catch (Exception e) {
      if (!channel.isEmpty()) {
        channel.get().disconnect();
      }
      if (!session.isEmpty()) {
        session.get().disconnect();
      }
      monitor.add(e);
      return false;
    }
  }

  @Override
  public OutputStream upload(String path, FtpClient.FtpFileType fileType) {
    throw new UnsupportedOperationException("Not implemented");
  }

  @Override
  public void upload(String path, byte[] data) {
    Session session = null;
    ChannelSftp channel = null;
    OutputStream out = null;
    try {
      session = jsch.getSession(username, host, port);
      session.setPassword(password);
      session.setConfig(config);
      maybeUserInfo.ifDefined(session::setUserInfo);
      maybeSetTimeout(session);
      session.connect();

      channel = (ChannelSftp) session.openChannel("sftp");
      channel.connect();

      out = channel.put(path);
      ByteSource.wrap(data).copyTo(out);
    } catch (IOException | JSchException | SftpException e) {
      throw new RuntimeException(e);
    } finally {
      closeQuietly(out);
      if (channel != null) {
        channel.disconnect();
      }
      if (session != null) {
        session.disconnect();
      }
    }
  }
  
  private void maybeSetTimeout(Session session) throws JSchException {
    if (timeout.isDefined()) {
      session.setTimeout(Math.toIntExact(timeout.getOrThrow().getMillis()));
    }
  }

}
