package com.kaching.util.http;

import java.util.List;

import org.apache.commons.codec.EncoderException;
import org.apache.commons.codec.net.URLCodec;

public class FormEncoder {

  private static final URLCodec urlCodec = new URLCodec();

  public static String formEncode(List<Parameter> params) {
    if (params.isEmpty()) {
      return "";
    }
    try {
      StringBuilder sb = new StringBuilder();
      for (Parameter p : params) {
        sb.append(p.getName()).append('=');
        if (p.getValue() != null) {
          sb.append(urlCodec.encode(p.getValue()));
        }
        sb.append('&');
      }
      sb.setLength(sb.length() - 1);
      return sb.toString();
    } catch (EncoderException e) {
      throw new RuntimeException(e);
    }
  }

}
