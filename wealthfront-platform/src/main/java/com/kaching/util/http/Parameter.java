package com.kaching.util.http;

import org.apache.http.NameValuePair;

import com.kaching.platform.common.Pair;

/**
 * An immutable pair encapsulating a name and a value.
 */
public final class Parameter extends Pair<String, String>
    implements NameValuePair {

  public Parameter(String name, String value) {
    super(name, value);
  }

  @Override
  public String getName() {
    return left;
  }

  @Override
  public String getValue() {
    return right;
  }

  @Override
  public String toString() {
    return left + "=" + right;
  }

}
