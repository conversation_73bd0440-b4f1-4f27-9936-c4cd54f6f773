package com.kaching.util.http;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.net.ssl.HttpsURLConnection;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Joiner;
import com.google.common.collect.ImmutableMap;
import com.kaching.platform.common.logging.Log;

public class HttpAPIConnectionImpl implements HttpAPIConnection {

  public static final String USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_10_2) AppleWebKit/537.36" +
      " (KHTML, like Gecko) Chrome/40.0.2214.111 Safari/537.36";

  private static final int RESPONSE_CODE_OK = 200;
  @VisibleForTesting static final Charset DEFAULT_CHARSET = Charset.defaultCharset();

  private static final Log logger = Log.getLog(HttpAPIConnectionImpl.class);

  @Override
  public String getPostParams(Map<String, String> paramsMap) {
    StringBuilder result = new StringBuilder();
    for (String param : paramsMap.keySet()) {
      if (result.length() != 0) {
        result.append("&");
      }
      result.append(param + "=" + paramsMap.get(param));
    }
    return result.toString();
  }

  @Override
  public String sendPost(String url, String postParams, int expectedStatus) {
    try {
      final URL obj = new URL(url);
      HttpURLConnection conn = (HttpURLConnection) obj.openConnection();

      conn.setUseCaches(false);
      conn.setRequestMethod("POST");
      conn.setRequestProperty("User-Agent", USER_AGENT);
      conn.setRequestProperty("Accept",
          "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
      conn.setRequestProperty("Connection", "keep-alive");
      conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
      conn.setRequestProperty("Content-Length", Integer.toString(postParams.length()));

      conn.setDoOutput(true);
      conn.setDoInput(true);

      try (DataOutputStream wr = new DataOutputStream(conn.getOutputStream())) {
        wr.writeBytes(postParams);
        wr.flush();
      }

      int responseCode = conn.getResponseCode();
      if (responseCode != expectedStatus) {
        throw new IllegalStateException(String.format("Got response code %s. %s", responseCode, parseResponseBody(conn,
            responseCode)));
      }

      try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
        String inputLine;
        List<String> responses = new ArrayList<>();
        while ((inputLine = in.readLine()) != null) {
          responses.add(inputLine);
        }
        return Joiner.on('\n').join(responses);
      }
    } catch (IOException e) {
      logger.error(e);
      throw new RuntimeException(e);
    }
  }

  @Override
  public String sendJSONPost(String url, String json, int expectedStatus) {
    try {
      final URL obj = new URL(url);
      HttpsURLConnection conn = (HttpsURLConnection) obj.openConnection();

      conn.setUseCaches(false);
      conn.setRequestMethod("POST");
      conn.setRequestProperty("Content-Type", "application/json");
      conn.setRequestProperty("Content-Length", Integer.toString(json.length()));
      conn.setDoOutput(true);
      conn.setDoInput(true);
      conn.setRequestProperty("Connection", "keep-alive");

      try (DataOutputStream wr = new DataOutputStream(conn.getOutputStream())) {
        wr.write(json.getBytes("UTF-8"));
        wr.flush();
      }

      int responseCode = conn.getResponseCode();
      if (responseCode != expectedStatus) {
        throw new IllegalStateException(String.format("Got response code %s. %s", responseCode, parseResponseBody(conn,
            responseCode)));
      }

      try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
        StringBuilder sb = new StringBuilder();
        String inputLine = in.readLine();
        while ((inputLine != null)) {
          sb.append(inputLine);
          inputLine = in.readLine();
        }
        return sb.toString();
      }
    } catch (IOException e) {
      logger.error(e);
      throw new RuntimeException(e);
    }
  }

  @Override
  public String sendJSONPost(String url, String json, int expectedStatus, Map<String, String> requestHeaders) {
    try {
      final URL obj = new URL(url);
      HttpsURLConnection conn = (HttpsURLConnection) obj.openConnection();

      conn.setUseCaches(false);
      conn.setRequestMethod("POST");
      conn.setRequestProperty("Content-Type", "application/json");
      conn.setRequestProperty("Content-Length", Integer.toString(json.length()));
      conn.setDoOutput(true);
      conn.setDoInput(true);
      conn.setRequestProperty("Connection", "keep-alive");
      for (String key : requestHeaders.keySet()) {
        conn.setRequestProperty(key, requestHeaders.get(key));
      }

      try (DataOutputStream wr = new DataOutputStream(conn.getOutputStream())) {
        wr.write(json.getBytes("UTF-8"));
        wr.flush();
      }

      int responseCode = conn.getResponseCode();
      if (responseCode != expectedStatus) {
        throw new IllegalStateException(String.format("Got response code %s. %s", responseCode, parseResponseBody(conn,
            responseCode)));
      }

      try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
        StringBuilder sb = new StringBuilder();
        String inputLine = in.readLine();
        while ((inputLine != null)) {
          sb.append(inputLine);
          inputLine = in.readLine();
        }
        return sb.toString();
      }
    } catch (IOException e) {
      logger.error(e);
      throw new RuntimeException(e);
    }
  }

  @Override
  public String sendPost(String url, String postParams, int expectedStatus, Map<String, String> requestHeaders) {
    try {
      final URL obj = new URL(url);
      HttpsURLConnection conn = (HttpsURLConnection) obj.openConnection();

      conn.setUseCaches(false);
      conn.setRequestMethod("POST");
      conn.setRequestProperty("User-Agent", USER_AGENT);
      conn.setRequestProperty("Accept",
          "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
      conn.setRequestProperty("Connection", "keep-alive");
      conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
      for (String key : requestHeaders.keySet()) {
        conn.setRequestProperty(key, requestHeaders.get(key));
      }
      conn.setRequestProperty("Content-Length", Integer.toString(postParams.length()));

      conn.setDoOutput(true);
      conn.setDoInput(true);

      try (DataOutputStream wr = new DataOutputStream(conn.getOutputStream())) {
        wr.writeBytes(postParams);
        wr.flush();
      }

      int responseCode = conn.getResponseCode();
      if (responseCode != expectedStatus) {
        throw new IllegalStateException(String.format("Got response code %s. %s", responseCode, parseResponseBody(conn,
            responseCode)));
      }

      try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
        String inputLine;
        List<String> responses = new ArrayList<>();
        while ((inputLine = in.readLine()) != null) {
          responses.add(inputLine);
        }
        return Joiner.on('\n').join(responses);
      }
    } catch (IOException e) {
      logger.error(e);
      throw new RuntimeException(e);
    }
  }

  @Override
  public String sendSOAPPost(String url, String postParams) {
    try {
      final URL obj = new URL(url);
      HttpsURLConnection conn = (HttpsURLConnection) obj.openConnection();

      conn.setUseCaches(false);
      conn.setRequestMethod("POST");
      conn.setRequestProperty("Content-Type", "text/xml; charset=utf-8");
      conn.setRequestProperty("Content-Length", Integer.toString(postParams.length()));

      conn.setDoOutput(true);
      conn.setDoInput(true);

      try (DataOutputStream wr = new DataOutputStream(conn.getOutputStream())) {
        wr.writeBytes(postParams);
        wr.flush();
      }

      try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
        String inputLine;
        List<String> responses = new ArrayList<>();
        while ((inputLine = in.readLine()) != null) {
          responses.add(inputLine);
        }
        return Joiner.on('\n').join(responses);
      }
    } catch (IOException e) {
      logger.error(e);
      throw new RuntimeException(e);
    }
  }

  @Override
  public String sendGet(String url) throws IOException {
    return sendGet(url, ImmutableMap.of());
  }

  @Override
  public String sendGet(String url, Map<String, String> requestProperties, Charset responseCharset) throws IOException {
    throwIfInsecure(url);
    return sendGetHelper(url, requestProperties, responseCharset);
  }

  @Override
  public String sendGet(String url, Map<String, String> requestProperties) throws IOException {
    throwIfInsecure(url);
    return sendGetHelper(url, requestProperties);
  }

  private void throwIfInsecure(String url) throws SecurityException {
   if (!secureRequestUrl(url)) {
      throw new SecurityException("You are attempting to make an insecure http request.");
   }
  }

  @Override
  public String sendInsecureInternalOnlyGet(String url) throws IOException {
    if (secureRequestUrl(url)) {
      throw new SecurityException("You are attempting to make a secure http request. Please use sendGet().");
    }

    return sendGetHelper(url, ImmutableMap.of());
  }

  private boolean secureRequestUrl(String url) {
    return url.startsWith("https");
  }

  @VisibleForTesting
  public String sendGetHelper(String url, Map<String, String> requestProperties) throws IOException {
    return sendGetHelper(url, requestProperties, DEFAULT_CHARSET);
  }

  @VisibleForTesting
  public String sendGetHelper(String url, Map<String, String> requestProperties, Charset charset) throws IOException {
    HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();

    conn.setRequestMethod("GET");
    conn.setUseCaches(false);
    conn.setRequestProperty("User-Agent", USER_AGENT);
    conn.setRequestProperty("Accept",
        "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
    for (Map.Entry<String, String> property: requestProperties.entrySet()) {
      conn.setRequestProperty(property.getKey(), property.getValue());
    }

    int responseCode = conn.getResponseCode();
    if (responseCode != RESPONSE_CODE_OK) {
      logger.warn("Non-200 response code received: %s", responseCode);
      throw new IllegalStateException(String.format("Got response code %s. %s", responseCode, parseResponseBody(conn,
          responseCode, charset)));
    }

    return parseResponseBody(conn, responseCode, charset);
  }

  @VisibleForTesting
  public String parseResponseBody(HttpURLConnection conn, int status) throws IOException {
    return parseResponseBody(conn, status, DEFAULT_CHARSET);
  }

  @VisibleForTesting
  public String parseResponseBody(HttpURLConnection conn, int status, Charset charset) throws IOException {
    final InputStream stream = status == RESPONSE_CODE_OK ? conn.getInputStream() : conn.getErrorStream();
    if (stream == null) {
      throw new IllegalStateException(String.format(
          "No %s stream found after getting response code %s", status == RESPONSE_CODE_OK ? "input" : "error", status));
    }
    try (BufferedReader in = new BufferedReader(new InputStreamReader(stream, charset))) {
      String inputLine;

      List<String> responses = new ArrayList<>();
      while ((inputLine = in.readLine()) != null) {
        responses.add(inputLine);
      }
      return Joiner.on('\n').join(responses);
    }
  }

  @Override
  public String sendHttpGet(String url) throws IOException {
    URL obj = new URL(url);
    HttpURLConnection conn = (HttpURLConnection) obj.openConnection();

    conn.setRequestMethod("GET");
    conn.setUseCaches(false);
    conn.setRequestProperty("User-Agent", USER_AGENT);
    conn.setRequestProperty("Accept",
        "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");

    int responseCode = conn.getResponseCode();
    if (responseCode != RESPONSE_CODE_OK) {
      throw new IllegalStateException(String.format("Got response code %s. %s", responseCode, parseResponseBody(conn,
          responseCode)));
    }

    try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
      String inputLine;

      List<String> responses = new ArrayList<>();
      while ((inputLine = in.readLine()) != null) {
        responses.add(inputLine);
      }
      return Joiner.on('\n').join(responses);
    }
  }

}
