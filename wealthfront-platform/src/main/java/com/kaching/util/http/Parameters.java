package com.kaching.util.http;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import javax.servlet.http.HttpServletRequest;

public class Parameters {

  /**
   * Gets around the awkwardness of http request parameters allowing multiple
   * values for a given key. Returns a flattened map of key/value pairs with
   * only the first value for each key.
   */
  public static Map<String, String> flattenedParamMap(HttpServletRequest req) {
    Map<String, String> result = new HashMap<>();
    @SuppressWarnings("unchecked")
    Map<String, String[]> requestParams = req.getParameterMap();
    for (Entry<String, String[]> e : requestParams.entrySet()) {
      String[] values = e.getValue();
      if (values != null && values.length > 0) {
        result.put(e.getKey(), values[0]);
      }
    }
    return result;
  }

  // returns the first value found in the map for a given key
  public static String get(Map<String, String[]> map, String key) {
    String[] vals = map.get(key);
    if (vals != null && vals.length > 0) {
      return vals[0];
    }
    return null;
  }

}
