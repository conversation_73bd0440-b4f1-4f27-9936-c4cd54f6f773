package com.kaching.util.http;

import com.google.inject.Provider;

abstract class DelegatingHttpClient extends QueryHttpClient {

  protected final Provider<HttpClient> httpClient;

  DelegatingHttpClient(
      Provider<HttpClient> httpClient,
      QueryStringFormatter formatter) {
    super(formatter);
    this.httpClient = httpClient;
  }

  public Provider<HttpClient> getHttpClientProvider() {
    return httpClient;
  }

}
