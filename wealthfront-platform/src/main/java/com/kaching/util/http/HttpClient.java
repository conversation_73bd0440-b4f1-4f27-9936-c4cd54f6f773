package com.kaching.util.http;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.util.List;
import java.util.Map;

import org.apache.commons.httpclient.Header;

/**
 * An HTTP "user-agent" to call trusted remote services.
 * <p/>
 * <p>Callers should not use an {@code HttpClient} with remote servers that
 * might be malicious unless the specific implementation claims to be defensive.
 */
public interface HttpClient {

  // TODO: Add methods for returning content as a String. This is important
  // for textual responses when the caller doesn't know in advance what
  // character encoding the response will use.

  /**
   * Sends a POST request to the URI with the given parameters.
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  byte[] post(URI uri, Parameter... parameters) throws IOException;

  /**
   * Sends a POST request to the URI with the given parameters.
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  byte[] post(URI uri, List<Parameter> parameters) throws IOException;

  /**
   * Sends a POST request to the URI with the given parameters.
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  byte[] post(
      URI uri, Map<String, String> requestHeaders,
      Parameter... parameters) throws IOException;

  /**
   * Sends a POST request to the URI with the given parameters.
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  byte[] post(
      URI uri, Map<String, String> requestHeaders,
      List<Parameter> parameters) throws IOException;

  /**
   * Sends a GET request to the URI with the given parameters.
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  byte[] get(URI uri, Parameter... parameters) throws IOException;

  /**
   * Sends a GET request to the URI with the given parameters.
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  InputStream getAsStream(URI uri, Parameter... parameters) throws IOException;

  /**
   * Sends a GET request to the URI with the given parameters.
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  byte[] get(URI uri, List<Parameter> parameters) throws IOException;

  /**
   * Sends a GET request to the URI with the given parameters.
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  InputStream getAsStream(URI uri, List<Parameter> parameters) throws IOException;

  /**
   * Sends a GET request to the URI with the given parameters.
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  Header getResponseHeader(
      URI uri, Map<String, String> requestHeaders,
      List<Parameter> parameters, String headerKey) throws IOException;

  /**
   * Sends a GET request to the URI with the given parameters.
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  byte[] get(
      URI uri, Map<String, String> requestHeaders,
      Parameter... parameters) throws IOException;

  /**
   * Sends a GET request to the URI with the given parameters.
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  byte[] get(
      URI uri, Map<String, String> requestHeaders,
      List<Parameter> parameters) throws IOException;

  /**
   * PUTs data at URL
   *
   * @throws IllegalStatusCode if an invalid status code was received
   */
  byte[] put(URI uri, String data) throws IOException;

}
