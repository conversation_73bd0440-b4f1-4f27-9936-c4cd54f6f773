package com.kaching.util.http;

import static com.google.common.collect.Lists.newArrayListWithCapacity;

import java.util.List;

public class QP0P1Formatter extends AbstractQueryStringFormatter {

  @Override
  public List<Parameter> format(String queryName, List<String> parameters) {
    List<Parameter> params = newArrayListWithCapacity(1 + parameters.size());
    params.add(new Parameter("q", queryName));
    for (int i = 0; i < parameters.size(); i++) {
      String value = parameters.get(i);
      if (value != null) {
        params.add(new Parameter("p" + i, value));
      }
    }
    return params;
  }
}
