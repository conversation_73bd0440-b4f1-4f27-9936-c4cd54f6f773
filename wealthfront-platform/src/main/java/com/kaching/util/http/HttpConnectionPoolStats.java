package com.kaching.util.http;

import static com.kaching.platform.monitoring.VarzValue.VarzType.GAUGE;
import static java.util.stream.Collectors.toMap;

import java.util.Map;
import java.util.function.Function;

import org.apache.http.conn.routing.HttpRoute;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.pool.PoolStats;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.kaching.platform.monitoring.VarzValue;

@Singleton
public class HttpConnectionPoolStats {

  private final PoolingHttpClientConnectionManager pool;

  @Inject
  public HttpConnectionPoolStats(PoolingHttpClientConnectionManager pool) {
    this.pool = pool;
  }

  @VarzValue(GAUGE)
  public int getAvailable() {
    return pool.getTotalStats().getAvailable();
  }

  @VarzValue(GAUGE)
  public int getLeased() {
    return pool.getTotalStats().getLeased();
  }

  @VarzValue(GAUGE)
  public int getMax() {
    return pool.getTotalStats().getMax();
  }

  @VarzValue(GAUGE)
  public int getPending() {
    return pool.getTotalStats().getPending();
  }

  public Map<HttpRoute, PoolStats> getStatsByRoute() {
    return pool.getRoutes().stream()
        .collect(toMap(
            Function.identity(),
            pool::getStats
        ));
  }

}
