package com.kaching.util.http;

import static com.kaching.platform.common.logging.Log.getLog;
import static java.lang.String.format;
import static java.util.regex.Pattern.compile;

import java.util.regex.Pattern;

import org.apache.commons.httpclient.HttpMethodBase;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.URI;
import org.apache.commons.httpclient.URIException;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.http.client.methods.HttpUriRequest;

import com.kaching.platform.common.logging.Log;

public class HttpPathUtils {

  private static final Log log = getLog(HttpPathUtils.class);
  private static final Pattern allCapsOrNums = compile("[A-Z0-9+\\.\\-\\^]*");

  public static String getRequestLogPath(HttpUriRequest request) {
    if (request == null) {
      return "NR";
    }
    return getRequestLogPath(request.getURI().getPath());
  }

  public static String getRequestLogPath(String path) {
    if (path.isEmpty()) {
      return "NP";
    }
    if (path.length() < 2) {
      return path;
    }
    StringBuilder sb = new StringBuilder();
    for (String element : path.split("/|\\.")) {
      if (validLogPathElement(element)) {
        sb.append("/").append(element);
      }
    }
    return sb.toString();
  }

  private static boolean validLogPathElement(String element) {
    if (null == element) {
      return false;
    }
    return !allCapsOrNums.matcher(element).matches();
  }

  public static String getRequestLogPath(HttpMethodBase request) {
    String uriString = getUri(request);
    String path = request.getPath();
    if (path.isEmpty() || path.trim().length() == 1) {
      return format("%s%s", uriString, toString(request));
    }
    return format("%s%s", uriString, getRequestLogPath(path));
  }

  private static String getUri(HttpMethodBase request) {
    try {
      URI uri = request.getURI();
      int port = uri.getPort();
      return format("%s:%s", uri.getHost(), port == -1 ? 80 : port);
    } catch (URIException e) {
      log.error(e, "on parsing request");
      return "ERR";
    }
  }

  private static String toString(HttpMethodBase request) {
    String queryName = "/NQ";
    if (request instanceof PostMethod) {
      PostMethod post = (PostMethod) request;
      NameValuePair param = post.getParameter("q");
      if (null != param) {
        queryName = format("/%s=%s", param.getName(), param.getValue());
      }
    }
    return queryName;
  }

}
