package com.kaching.api;

/**
 * Used to compare a model against a previous version of that model.
 * 
 * LOOSER   - New model *accepts more* than it used to.
 *          - The new model can losslessly deserialize data serialized with the old version of the model.
 *          - For backwards compatibility, consumers must be updated with the newer model before producers produce data in the new format.
 *          - Examples: adding a new enum value, adding nullable field, changing enum to string
 * STRICTER - New model *accepts less* than it used to.
 *          - Data serialized with the old model may not be readable to consumers using the new model 
 *          - For backwards compatibility, producers should be updated with the newer model before consumers
 *          - Examples: removing enum value, making field non-null
 * EQUAL    - Models have the exact same wire structure
 * 
 * INCOMPATIBLE - Data serialized with the old version of the model is NOT readable by the new version
 *              - Example: adding a new, required field to an object
 *              - An API change where some things are LOOSER and some are STRICTER is said to be INCOMPATIBLE, because it's often
 *                not possible to properly sequence updating both producers and consumers
 * 
 * Note 1: For a backend service exposing a Query with parameters and a return type, 
 *         the service *consumes* the parameter types and *produces* the return type.
 * Note 2: Note the word 'losslessly'- some API serialization schemes are lossy and ignore or have special treatment for unrecognized data
 *         (new enum values, unexpected fields). This isn't considered in the definitions above.
 */
public enum WireFormatComparison {
  INCOMPATIBLE,
  STRICTER,
  LOOSER;
}
