package com.kaching.api;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;
import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.kaching.api.ExposeType.RewriteNamespace.INLINE_INTO_CHILDREN;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.reflect.TypeUtils.getTypeArguments;

import java.lang.annotation.Annotation;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.lang.reflect.Parameter;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.lang.reflect.TypeVariable;
import java.lang.reflect.WildcardType;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Stream;

import javax.annotation.Nonnull;

import org.apache.commons.lang3.reflect.TypeLiteral;
import org.apache.commons.lang3.reflect.TypeUtils;
import org.joda.time.Days;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.inject.util.Types;
import com.kaching.api.JavaApiQuerySchema.JavaQueryParameter;
import com.kaching.api.JavaApiType.JavaCollectionType;
import com.kaching.api.JavaApiType.JavaEnumType;
import com.kaching.api.JavaApiType.JavaMapType;
import com.kaching.api.JavaApiType.JavaObjectField;
import com.kaching.api.JavaApiType.JavaObjectType;
import com.kaching.api.JavaApiType.JavaOptionType;
import com.kaching.api.JavaApiType.JavaSimpleType;
import com.kaching.api.JavaApiType.JavaSimpleType.SerializedType;
import com.kaching.api.JavaApiTypesBuilder.TypeKey;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.platform.common.Strings;
import com.kaching.platform.converters.Instantiate;
import com.kaching.platform.converters.Optional;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.queryengine.NoConcurrentExecution;
import com.kaching.platform.queryengine.Owned;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.SecureField;
import com.kaching.platform.queryengine.admin.Param;
import com.kaching.util.time.Year;
import com.twolattes.json.Entity;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;
import com.twolattes.json.types.JsonType;
import com.twolattes.records.RecordDetector;
import com.twolattes.records.RecordDetectorImpl;
import com.twolattes.records.Records;

public class JavaApiSchemaIntrospector {

  private static final Set<Class<?>> SIMPLE_NUMERIC_TYPES = ImmutableSet.of(
      BigDecimal.class,
      Float.class, float.class,
      Double.class, double.class
  );

  private static final Set<Class<?>> SIMPLE_INTEGER_TYPES = ImmutableSet.of(
      Long.class, long.class,
      Integer.class, int.class,
      Short.class, short.class,
      Byte.class, byte.class,
      BigInteger.class,
      Year.class,
      Days.class
  );

  private static final Set<Class<?>> SIMPLE_BOOLEAN_TYPES = ImmutableSet.of(
      Boolean.class, boolean.class
  );

  private static final Set<Class<? extends Annotation>> IGNORED_QUERY_PARAMETER_ANNOTATIONS = ImmutableSet.of(
      Optional.class,
      SuppressWarnings.class,
      NoConcurrentExecution.class,
      Param.class
  );

  private static final Set<Class<? extends Annotation>> IGNORED_QUERY_FIELD_ANNOTATIONS = ImmutableSet.of(
      Deprecated.class,
      SuppressWarnings.class
  );

  @VisibleForTesting
  static RecordDetector recordDetector = new RecordDetectorImpl();

  public static void introspectQuery(Errors errors, Class<? extends Query<?>> queryClass, JavaApiTypesBuilder builder) {
    List<Constructor<?>> constructors = Arrays.asList(queryClass.getDeclaredConstructors());
    Constructor<?> constructor = constructors.size() == 1 ? constructors.get(0) :
        constructors.stream()
            .filter(c -> c.getAnnotation(Instantiate.class) != null)
            .findAny()
            .orElseThrow(() -> new IllegalArgumentException(Strings.format("Query %s has multiple constructors but no @Instantiate")));

    List<JavaQueryParameter> parameters = Arrays.stream(constructor.getParameters())
        .flatMap(param -> {
          boolean nullable = param.isAnnotationPresent(Optional.class);
          String paramName = param.getName();
          Field field;
          try {
            field = queryClass.getDeclaredField(paramName);
          } catch (NoSuchFieldException e) {
            errors.addMessage("Query %s with constructor parameter %s should have field with same name", queryClass, paramName);
            return Stream.empty();
          }
          TypeKey typeKey = new TypeKey(param.getParameterizedType(), nullable);
          Option<JavaApiType> maybeApiType = introspectType(errors, typeKey, builder);
          if (maybeApiType.isEmpty()) {
            errors.addMessage("Unable to introspect " + typeKey);
            return Stream.empty();
          }
          JavaApiType apiType = maybeApiType.getOrThrow();
          EnumSet<JavaQueryParameter.Flag> flags = getFlags(errors, param, field);
          return Stream.of(new JavaQueryParameter(paramName, apiType, flags));
        }).collect(toList());
    Type javaReturnType = extractTypeArguments(queryClass, Query.class, 0);
    JavaApiType returnType = introspectTypeOrThrow(new TypeKey(javaReturnType, false), builder);
    JavaApiQuerySchema querySchema = new JavaApiQuerySchema(queryClass, parameters, returnType);
    builder.addQuery(querySchema);
  }

  private static EnumSet<JavaQueryParameter.Flag> getFlags(Errors errors, Parameter param, Field field) {
    EnumSet<JavaQueryParameter.Flag> flags = EnumSet.noneOf(JavaQueryParameter.Flag.class);
    for (Annotation annotation : Arrays.asList(field.getDeclaredAnnotations())) {
      if (annotation instanceof SecureField) {
        flags.add(JavaQueryParameter.Flag.SECURE_FIELD);
      } else if (IGNORED_QUERY_FIELD_ANNOTATIONS.contains(annotation.annotationType())) {
        // do nothing
      } else {
        errors.addMessage("Unknown annotation on field %s: %s", field.getName(), annotation.annotationType());
      }
    }
    for (Annotation annotation : Arrays.asList(param.getDeclaredAnnotations())) {
      if (annotation instanceof Owned) {
        switch (param.getAnnotation(Owned.class).value()) {
          case ENFORCING:
            flags.add(JavaQueryParameter.Flag.OWNED_ENFORCING);
            break;
          case PERMISSIVE:
            break;
          default:
            errors.addMessage("Unknown Owned value: " + param.getAnnotation(Owned.class).value());
            break;
        }
      } else if (IGNORED_QUERY_PARAMETER_ANNOTATIONS.contains(annotation.annotationType())) {
        // do nothing
      } else {
        errors.addMessage("Unknown annotation on param %s: %s", param.getName(), annotation.annotationType());
      }
    }
    if (field.getType().isAnnotationPresent(SecureField.class)) {
      flags.add(JavaQueryParameter.Flag.SECURE_FIELD);
    }
    if (param.isAnnotationPresent(Optional.class) || param.getType().equals(Option.class)) {
      flags.add(JavaQueryParameter.Flag.OPTIONAL);
    }
    return flags;
  }

  @VisibleForTesting
  static JavaApiQuerySchema introspectQueryOrThrow(Class<? extends Query<?>> queryClass, JavaApiTypesBuilder builder) {
    Errors errors = new Errors();
    introspectQuery(errors, queryClass, builder);
    errors.throwIfHasErrors();
    return checkNotNull(builder.getQueries().get(queryClass));
  }

  public static Option<JavaApiType> introspectType(Errors errors, TypeKey typeKey, JavaApiTypesBuilder builder) {
    typeKey = builder.override(typeKey);
    for (JavaApiType type : builder.getType(typeKey)) {
      return Option.some(type);
    }
    for (JavaApiType type : generateOptionalType(typeKey, builder)) {
      return Option.some(type);
    }
    for (JavaApiType type : generateHardCodedKachingQueryType(typeKey, builder)) {
      return Option.some(type);
    }
    for (JavaApiType type : generateJsonType(errors, typeKey, builder, emptyList())) {
      return Option.some(type);
    }
    for (JavaApiType type : generateSimpleStringInstantiatedType(typeKey, builder)) {
      return Option.some(type);
    }
    errors.addMessage("Cannot determine how to marshall " + typeKey);
    return Option.none();
  }

  public static JavaApiType introspectTypeOrThrow(TypeKey typeKey, JavaApiTypesBuilder builder) {
    Errors errors = new Errors();
    Option<JavaApiType> returnType = introspectType(errors, typeKey, builder);
    errors.throwIfHasErrors();
    return returnType.getOrThrow();
  }

  private static Option<JavaApiType> generateJsonType(Errors errors, TypeKey typeKey, JavaApiTypesBuilder builder,
                                                      List<Class<? extends JsonType<?, ?>>> converterTypes) {
    typeKey = builder.override(typeKey);
    Option<JavaApiType> existing = builder.getType(typeKey);
    if (existing.isDefined()) {
      return existing;
    }
    Class<?> rawType = TypeUtils.getRawType(typeKey.type, null);
    if (rawType.isEnum()) {
      JavaEnumType newType = new JavaEnumType(typeKey.nullable, (Class<? extends Enum<?>>) rawType);
      builder.addType(newType);
      return Option.some(newType);
    }
    if (rawType.getAnnotation(Entity.class) != null) {
      return Option.some(generateJsonObject(errors, rawType, typeKey.nullable, builder));
    }
    if (rawType.getAnnotation(JsonClassDescription.class) != null) {
      return Option.some(generateJacksonObject(errors, rawType, typeKey.nullable, builder));
    }
    Option<JavaApiType> maybeSimpleType = generateSimpleType(typeKey);
    if (maybeSimpleType.isDefined()) {
      JavaApiType newType = maybeSimpleType.getOrThrow();
      builder.addType(typeKey, newType);
      return Option.some(newType);
    }
    if (rawType.isArray()) {
      JavaApiType arrayType = generateJavaArrayType(errors, typeKey, builder, converterTypes);
      builder.addType(typeKey, arrayType);
      return Option.some(arrayType);
    }
    for (JavaApiType parameterizedType : generateParameterizedJsonType(errors, typeKey, builder, converterTypes)) {
      return Option.some(parameterizedType);
    }
    Marshaller<?> marshaller = null;
    try {
      marshaller = createMarshaller(rawType);
    } catch (RuntimeException ex) {
      // do nothing
    }
    if (marshaller != null) {
      Class<? extends Json.Value> marshalledClass = marshaller.getMarshalledClass();
      Option<JavaApiType> newType = generateUsingMarshalledTwoLattesType(errors, marshalledClass, typeKey, Option.none());
      newType.ifDefined(builder::addType);
      return newType;
    } else if (!converterTypes.isEmpty()) {
      if (converterTypes.size() != 1) {
        errors.addMessage("Unexpected multiple converters for non-Map type: " + typeKey);
      }
      Class<? extends JsonType<?, ?>> converterClass = converterTypes.get(0);
      Type convertedType = extractTypeArguments(converterClass, JsonType.class, 0);
      Class<? extends Json.Value> jsonType = (Class<? extends Json.Value>) extractTypeArguments(converterClass, JsonType.class, 1);
      if (!convertedType.equals(typeKey.type)) {
        errors.addMessage("Supplied converter %s cannot marshall type %s, only %s", converterClass, typeKey.type, convertedType);
        return Option.none();
      }
      Option<JavaApiType> newType = generateUsingMarshalledTwoLattesType(errors, jsonType, typeKey, Option.some(converterClass));
      newType.ifDefined(builder::addType);
      return newType;
    } else {
      return Option.none();
    }
  }

  private static Option<JavaApiType> generateSimpleStringInstantiatedType(TypeKey typeKey, JavaApiTypesBuilder builder) {
    Class<?> rawType = TypeUtils.getRawType(typeKey.type, null);
    try {
      rawType.getDeclaredConstructor(String.class);
      JavaSimpleType newType = new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.STRING);
      builder.addType(newType);
      return Option.some(newType);
    } catch (NoSuchMethodException e) {
      return Option.none();
    }
  }

  private static Option<JavaApiType> generateHardCodedKachingQueryType(TypeKey typeKey, JavaApiTypesBuilder builder) {
    if (typeKey.type.equals(new TypeLiteral<Class<? extends ServiceKind>>() {}.getType()) || typeKey.type.equals(ServiceKind.class)) {
      JavaSimpleType type = new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.STRING);
      builder.addType(type);
      return Option.some(type);
    }
    return Option.none();
  }

  private static Option<JavaApiType> generateOptionalType(TypeKey typeKey, JavaApiTypesBuilder builder) {
    Class<?> rawType = TypeUtils.getRawType(typeKey.type, null);
    if (rawType.equals(Option.class)) {
      checkArgument(!typeKey.nullable, "An Option<> reference should be never itself be nullable: " + typeKey);
      TypeKey valueType = new TypeKey(simplifyTypeArgs((ParameterizedType) typeKey.type).get(0), true);
      JavaApiType apiValueType = introspectTypeOrThrow(valueType, builder);
      JavaOptionType optionType = new JavaOptionType(typeKey.type, valueType.type, apiValueType);
      builder.addType(typeKey, optionType);
      return Option.some(optionType);
    }
    return Option.none();
  }

  private static Option<JavaApiType> generateSimpleType(TypeKey typeKey) {
    Class<?> simpleRawType = TypeUtils.getRawType(typeKey.type, null);
    if (Identifier.class.isAssignableFrom(simpleRawType)) {
      Type identifiedType = extractTypeArguments(simpleRawType, Identifier.class, 0);
      simpleRawType = TypeUtils.getRawType(identifiedType, null);
    }
    if (SIMPLE_INTEGER_TYPES.contains(simpleRawType)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.INTEGER));
    } else if (SIMPLE_NUMERIC_TYPES.contains(simpleRawType)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.NUMBER));
    } else if (SIMPLE_BOOLEAN_TYPES.contains(simpleRawType)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.BOOLEAN));
    } else if (String.class.equals(simpleRawType)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.STRING));
    } else if (Json.Object.class.equals(simpleRawType)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.ANY_JSON_OBJECT));
    } else if (Json.Value.class.equals(simpleRawType)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.ANY_JSON_VALUE));
    } else if (JsonNode.class.equals(simpleRawType)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, Json.Value.class, SerializedType.ANY_JSON_VALUE));
    }
    return Option.none();
  }

  private static Option<JavaApiType> generateUsingMarshalledTwoLattesType(Errors errors,
      Class<? extends Json.Value> marshalledClazz,
      TypeKey typeKey,
                                                                          Option<Class<? extends JsonType<?, ?>>> converterClass) {
    if (marshalledClazz.equals(Json.String.class)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.STRING));
    } else if (marshalledClazz.equals(Json.Number.class)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.NUMBER));
    } else if (marshalledClazz.equals(Json.Boolean.class)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.BOOLEAN));
    } else if (marshalledClazz.equals(Json.Object.class)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.ANY_JSON_OBJECT));
    } else if (marshalledClazz.equals(Json.Value.class)) {
      return Option.some(new JavaSimpleType(typeKey.nullable, typeKey.type, SerializedType.ANY_JSON_VALUE));
    } else {
      errors.addMessage("Custom marshalling to Null or Array types not supported: %s, %s", marshalledClazz, typeKey.type);
      return Option.none();
    }
  }

  public static List<Type> simplifyTypeArgs(ParameterizedType type) {
    return Arrays.stream(type.getActualTypeArguments())
        .map(typeArg -> {
          if (typeArg instanceof WildcardType) {
            WildcardType wildcardType = (WildcardType) typeArg;
            if (wildcardType.getUpperBounds().length == 1 &&
                wildcardType.getLowerBounds().length == 0 &&
                !wildcardType.getUpperBounds()[0].equals(Object.class)) {
              return wildcardType.getUpperBounds()[0];
            } else {
              throw new IllegalArgumentException("Unsupported wildcard type: " + typeArg);
            }
          } else {
            return typeArg;
          }
        }).collect(toList());
  }

  private static Option<JavaApiType> generateParameterizedJsonType(Errors errors,
      TypeKey typeKey,
      JavaApiTypesBuilder builder,
                                                                   List<Class<? extends JsonType<?, ?>>> converterTypes) {
    if (!(typeKey.type instanceof ParameterizedType)) {
      return Option.none();
    }
    ParameterizedType parameterizedType = (ParameterizedType) typeKey.type;
    Class<?> rawType = (Class<?>) parameterizedType.getRawType();
    List<Type> simplifiedTypeArgs = simplifyTypeArgs(parameterizedType);
    if (Map.class.isAssignableFrom(rawType) && simplifiedTypeArgs.size() == 2) {
      TypeKey javaKeyType = new TypeKey(simplifiedTypeArgs.get(0), false);
      TypeKey javaValueType = new TypeKey(simplifiedTypeArgs.get(1), true);

      List<Class<? extends JsonType<?, ?>>> keyConverters = converterTypes.stream()
          .filter(clazz -> extractTypeArguments(clazz, JsonType.class, 0).equals(javaKeyType.type))
          .collect(toList());
      List<Class<? extends JsonType<?, ?>>> valueConverters = converterTypes.stream()
          .filter(clazz -> extractTypeArguments(clazz, JsonType.class, 0).equals(javaValueType.type))
          .collect(toList());

      JavaApiType apiKeyType = generateJsonType(errors, javaKeyType, builder, keyConverters)
          .getOrThrow(Strings.format("Unable to determine how key of Map type %s is marshalled", javaKeyType));
      JavaApiType apiValueType = generateJsonType(errors, javaValueType, builder, valueConverters)
          .getOrThrow(Strings.format("Unable to determine how value of Map type %s is marshalled", javaValueType));
      ParameterizedType simplifiedDeclaredType = Types.newParameterizedType(rawType, javaKeyType.type, javaValueType.type);
      JavaMapType mapType = new JavaMapType(typeKey.nullable, simplifiedDeclaredType, javaKeyType.type, javaValueType.type,
          apiKeyType, apiValueType);
      builder.addType(typeKey, mapType);
      return Option.some(mapType);
    } else if (Collection.class.isAssignableFrom(rawType)) {
      TypeKey javaValueType = new TypeKey(simplifiedTypeArgs.get(0), true);
      JavaApiType apiValueType = generateJsonType(errors, javaValueType, builder, converterTypes)
          .getOrThrow(Strings.format("Unable to determine how value of Collection type %s is marshalled", javaValueType));

      ParameterizedType simplifiedDeclaredType = Types.newParameterizedType(rawType, javaValueType.type);
      JavaCollectionType collectionType = new JavaCollectionType(typeKey.nullable, simplifiedDeclaredType, javaValueType.type, apiValueType);
      builder.addType(typeKey, collectionType);
      return Option.some(collectionType);
    } else if (rawType.equals(Option.class)) {
      errors.addMessage("Option<> should never appear inside of a json entity");
      return Option.none();
    }
    return Option.none();
  }

  private static JavaApiType generateJavaArrayType(Errors errors,
                                                   TypeKey typeKey,
                                                   JavaApiTypesBuilder builder,
                                                   List<Class<? extends JsonType<?, ?>>> converterTypes) {
    Class<?> valueType = checkNotNull((Class<?>) typeKey.type).getComponentType();
    TypeKey arrayValueTypeKey = new TypeKey(valueType, !valueType.isPrimitive());
    JavaApiType apiValueType = generateJsonType(errors, arrayValueTypeKey, builder, converterTypes)
        .getOrThrow(Strings.format("Unable to determine how value of Array type %s is marshalled", arrayValueTypeKey));
    return new JavaApiType.JavaArrayType(typeKey.nullable, typeKey.type, valueType, apiValueType);
  }

  private static JavaObjectType generateJsonObject(Errors errors, Class<?> clazz, boolean isNullable, JavaApiTypesBuilder schemaBuilder) {
    for (JavaApiType alreadyGenerated : schemaBuilder.getType(new TypeKey(clazz, isNullable))) {
      if (alreadyGenerated instanceof JavaObjectType) {
        return (JavaObjectType) alreadyGenerated;
      }
    }
    boolean isAbstract = Modifier.isAbstract(clazz.getModifiers());

    Entity entity = clazz.getAnnotation(Entity.class);

    List<Class<?>> entitySuperclassesDesc = getTwoLattesSuperclassesDesc(clazz);
    String discriminatorName = null;
    if (!entity.discriminatorName().equals("")) {
      discriminatorName = entity.discriminatorName();
    } else if (!entitySuperclassesDesc.isEmpty()) {
      discriminatorName = entitySuperclassesDesc.get(0).getAnnotation(Entity.class).discriminatorName();
      if (discriminatorName.equals("")) {
        discriminatorName = null;
      }
    }

    String discriminatorValue = entity.discriminator().equals("") ? null : entity.discriminator();
    JavaObjectType newType = new JavaObjectType(isNullable, clazz, discriminatorName, discriminatorValue, isAbstract);
    schemaBuilder.addType(newType);

    List<Pair<Field, Value>> javaFields;
    if (recordDetector.isRecord(clazz)) {
      javaFields = Records.getAnnotatedFields(clazz).stream()
          .map(entry -> Pair.of(entry.getKey(), entry.getValue()))
          .collect(toList());
    } else {
      List<Class<?>> classesWithFields = new ArrayList<>();
      classesWithFields.add(clazz);
      for (Class<?> superclass : Lists.reverse(getTwoLattesSuperclassesDesc(clazz))) {
        if (superclass.isAnnotationPresent(ExposeType.class) && superclass.getAnnotation(ExposeType.class).namespace() == INLINE_INTO_CHILDREN) {
          classesWithFields.add(superclass);
        } else {
          break;
        }
      }
      javaFields = Lists.reverse(classesWithFields).stream()
          .flatMap(c -> Arrays.stream(c.getDeclaredFields()))
          .filter(field -> field.getAnnotation(Value.class) != null)
          .map(field -> Pair.of(field, field.getAnnotation(Value.class)))
          .collect(toList());
    }

    List<JavaObjectField> fields = javaFields.stream()
        .flatMap(pair -> {
          Field field = pair.left;
          Value value = pair.right;

          String apiName = value.name().equals("") ? field.getName() : value.name();
          Type javaType = field.getGenericType();
          checkDisallowedJsonFieldTypes(javaType);

          boolean nullable;
          if (TypeUtils.getRawType(javaType, null).isPrimitive()) {
            nullable = false;
          } else {
            nullable = value.nullable();
          }

          List<Class<? extends JsonType<?, ?>>> converterTypes = new ArrayList<>();
          if (!value.type().equals(JsonType.class)) {
            converterTypes.add((Class<? extends JsonType<?, ?>>) value.type());
          }
          converterTypes.addAll(Arrays.asList(value.types()));
          Option<JavaApiType> javaApiType = generateJsonType(errors, new TypeKey(javaType, nullable), schemaBuilder, converterTypes);
          if (javaApiType.isEmpty()) {
            errors.addMessage("Introspection failed to find a valid type for %s", javaType);
            return Stream.empty();
          }

          return Stream.of(new JavaObjectField(field.getName(), apiName, javaType, javaApiType.getOrThrow(), value.optional(), converterTypes));
        }).collect(toList());
    newType.setFields(fields);

    List<Class<?>> nonInlinedSuperclasses = entitySuperclassesDesc.stream()
        .filter(superclass -> !superclass.isAnnotationPresent(ExposeType.class) || superclass.getAnnotation(ExposeType.class).namespace() != INLINE_INTO_CHILDREN)
        .collect(toList());

    if (!nonInlinedSuperclasses.isEmpty()) {
      List<JavaObjectType> superclassesGenerated = nonInlinedSuperclasses.stream()
          .map(superclass -> generateJsonObject(errors, superclass, isNullable, schemaBuilder))
          .collect(toList());
      JavaObjectType parent = Iterables.getLast(superclassesGenerated);
      newType.linkParent(parent);
    }
    if (entity.subclasses().length > 0) {
      Arrays.asList(entity.subclasses()).forEach(subclass -> generateJsonObject(errors, subclass, isNullable, schemaBuilder));
    }
    return newType;
  }

  private static JavaObjectType generateJacksonObject(Errors errors, Class<?> clazz, boolean isNullable, JavaApiTypesBuilder schemaBuilder) {
    for (JavaApiType alreadyGenerated : schemaBuilder.getType(new TypeKey(clazz, isNullable))) {
      if (alreadyGenerated instanceof JavaObjectType) {
        return (JavaObjectType) alreadyGenerated;
      }
    }

    checkArgument(clazz.isAnnotationPresent(JsonClassDescription.class), "Not a Jackson entity: " + clazz);
    boolean isAbstract = Modifier.isAbstract(clazz.getModifiers());
    List<Class<?>> entitySuperclassesDesc = getJacksonSuperclassesDesc(clazz);

    Class<?> familyMemberWithTypeInfo = null;
    if (clazz.isAnnotationPresent(JsonTypeInfo.class)) {
      familyMemberWithTypeInfo = clazz;
    } else if (!entitySuperclassesDesc.isEmpty() && entitySuperclassesDesc.get(0).isAnnotationPresent(JsonTypeInfo.class)) {
      familyMemberWithTypeInfo = entitySuperclassesDesc.get(0);
    }

    String discriminatorName = null;
    JsonTypeInfo typeInfo = null;
    BiMap<Class<?>, String> familyMembersToDiscriminators = HashBiMap.create();
    if (familyMemberWithTypeInfo != null) {
      checkArgument(familyMemberWithTypeInfo.isAnnotationPresent(JsonSubTypes.class), "Jackson class %s has a JsonTypeInfo annotation but no " +
          "JsonSubTypes annotation", clazz);
      typeInfo = familyMemberWithTypeInfo.getAnnotation(JsonTypeInfo.class);
      checkArgument(typeInfo.include() == JsonTypeInfo.As.PROPERTY, "Jackson entities with type info not included as PROPERTY not supported: %s, %s",
          clazz, typeInfo.include());
      checkArgument(typeInfo.use() == JsonTypeInfo.Id.NAME, "Jackson entities must use the name property for polymorphism: %s, %s", clazz, typeInfo.use());
      checkArgument(!typeInfo.property().equals(""), "Polymorphic jackson entity %s has empty discriminator name", clazz);
      discriminatorName = typeInfo.property();

      for (JsonSubTypes.Type type : familyMemberWithTypeInfo.getAnnotation(JsonSubTypes.class).value()) {
        if (type.value().equals(typeInfo.defaultImpl())) {
          continue;
        }
        familyMembersToDiscriminators.put(type.value(), type.name());
      }
    }

    String discriminatorValue;
    if (typeInfo != null) {
      checkArgument(isAbstract || familyMembersToDiscriminators.get(clazz) != null, "JsonTypeInfo exists for jackson ancestor class %s, but class %s " +
              "is not abstract and is not found in JsonSubTypes (can't find discriminator): %s", familyMemberWithTypeInfo, clazz, familyMembersToDiscriminators.keySet());
      discriminatorValue = isAbstract ? null : familyMembersToDiscriminators.get(clazz);
    } else {
      discriminatorValue = null;
    }
    JavaObjectType newType = new JavaObjectType(isNullable, clazz, discriminatorName, discriminatorValue, isAbstract);
    schemaBuilder.addType(newType);

    List<JavaObjectField> fields = Arrays.stream(clazz.getDeclaredFields())
        .filter(field -> field.isAnnotationPresent(JsonProperty.class))
        .map(field -> {
          JsonProperty annotation = field.getAnnotation(JsonProperty.class);
          checkArgument(annotation.access() == JsonProperty.Access.AUTO, "Jackson JsonProperty %s on %s is %s which is not supported",
              field.getName(), clazz, annotation.access());

          String apiName = annotation.value().equals("") ? field.getName() : annotation.value();
          Type javaType = field.getGenericType();
          checkDisallowedJsonFieldTypes(javaType);

          boolean nullable;
          boolean required;
          if (field.getType().isPrimitive()) {
            nullable = false;
            required = true;
          } else if (field.isAnnotationPresent(Nonnull.class)) {
            nullable = false;
            required = true;
          } else {
            nullable = true;
            required = false;
          }
          JavaApiType javaApiType = generateJsonType(errors, new TypeKey(javaType, nullable), schemaBuilder, emptyList())
              .getOrThrow(Strings.format("Introspection failed to find a valid type for %s", javaType));

          return new JavaObjectField(field.getName(), apiName, javaType, javaApiType, !required, emptyList());
        }).collect(toList());
    newType.setFields(fields);

    if (!entitySuperclassesDesc.isEmpty()) {
      List<JavaObjectType> superclassesGenerated = entitySuperclassesDesc.stream()
          .map(superclass -> generateJacksonObject(errors, superclass, isNullable, schemaBuilder))
          .collect(toList());
      JavaObjectType parent = Iterables.getLast(superclassesGenerated);
      newType.linkParent(parent);
    }

    familyMembersToDiscriminators.keySet().forEach(familyMember -> generateJacksonObject(errors, familyMember, isNullable, schemaBuilder));
    return newType;
  }

  @VisibleForTesting
  static boolean canBeNarrowedToIntegerType(Type javaType) {
    Class<?> rawType = TypeUtils.getRawType(javaType, null);
    if (SIMPLE_INTEGER_TYPES.contains(rawType)) {
      return true;
    }
    if (Identifier.class.isAssignableFrom(rawType)) {
      Type identifiedType = extractTypeArguments(rawType, Identifier.class, 0);
      if (SIMPLE_INTEGER_TYPES.contains(identifiedType)) {
        return true;
      }
    }
    return false;
  }

  private static void checkDisallowedJsonFieldTypes(Type type) {
    Class<?> rawType = TypeUtils.getRawType(type, null);
    if (rawType.equals(Option.class)) {
      throw new IllegalArgumentException("Option<> values are not allowed as direct parameters of json entities: " + type);
    }
  }

  @VisibleForTesting
  public static List<Class<?>> getTwoLattesSuperclassesDesc(Class<?> pointer) {
    return getSuperclassesWithPredicateDesc(pointer, clazz -> clazz.getAnnotation(Entity.class) != null);
  }

  @VisibleForTesting
  public static List<Class<?>> getJacksonSuperclassesDesc(Class<?> pointer) {
    return getSuperclassesWithPredicateDesc(pointer, clazz -> clazz.isAnnotationPresent(JsonClassDescription.class));
  }

  private static List<Class<?>> getSuperclassesWithPredicateDesc(Class<?> pointer, Predicate<Class<?>> predicate) {
    List<Class<?>> result = new ArrayList<>();
    do {
      if (pointer == null || pointer.getSuperclass() == null) {
        break;
      }
      pointer = pointer.getSuperclass();
      if (predicate.test(pointer)) {
        result.add(pointer);
      }
    } while (!pointer.equals(Object.class));
    Collections.reverse(result);
    return result;
  }

  private static Type extractTypeArguments(Type base, Class<?> superInterface, int paramNum) {
    Map<TypeVariable<?>, Type> typeArguments = getTypeArguments(base, superInterface);
    TypeVariable<? extends Class<?>> typeParameter = superInterface.getTypeParameters()[paramNum];
    return checkNotNull(typeArguments.get(typeParameter), "Unable to extract param %s from (%s extends %s): %s",
        paramNum, base, superInterface, typeArguments);
  }

}
