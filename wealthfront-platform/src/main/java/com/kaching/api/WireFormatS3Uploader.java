package com.kaching.api;

import static java.util.logging.Logger.getLogger;
import static java.util.stream.Collectors.toSet;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Set;
import java.util.logging.Logger;
import java.util.stream.Stream;

import org.apache.commons.io.FilenameUtils;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.SdkClientException;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.util.Md5Utils;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.inject.Guice;
import com.google.inject.Stage;
import com.kaching.platform.common.Option;

public class WireFormatS3Uploader {

  private static final Logger LOG = getLogger(WireFormatS3Uploader.class.getSimpleName());
  private static final String WIRE_FORMAT_BUCKET_NAME = "wf-wire-formats";

  @VisibleForTesting
  FileSystem fs = FileSystems.getDefault();

  private final String targetDirectory;

  public WireFormatS3Uploader(String targetDirectory) {
    this.targetDirectory = targetDirectory;
  }

  public static void main(String... args) throws IOException {
    Preconditions.checkArgument(args.length == 1, "exactly one argument required (target directory)");
    Preconditions.checkNotNull(args[0], "target directory (1st arg) must be defined");

    WireFormatS3Uploader wireFormatS3Uploader = new WireFormatS3Uploader(args[0]);
    Guice.createInjector(Stage.DEVELOPMENT).injectMembers(WireFormatS3Uploader.class);
    wireFormatS3Uploader.uploadFilesToS3();
  }

  @VisibleForTesting
  void uploadFilesToS3() throws IOException {
    LOG.info("Scanning " + targetDirectory + " for wire formats to upload...");
    Path wireFormatsPath = fs.getPath(targetDirectory);
    if (Files.notExists(wireFormatsPath)) {
      LOG.warning("Exiting. Could not find path " + wireFormatsPath);
      return;
    }
    Option<AmazonS3> maybeAmazonS3 = Option.none();
    try (Stream<Path> wireFormatPathStream = Files.walk(wireFormatsPath)) {
      Set<Path> wireFormatPaths = wireFormatPathStream
          .filter(Files::isRegularFile)
          .filter(path -> FilenameUtils.getExtension(path.toString()).equals("gz"))
          .collect(toSet());
      maybeAmazonS3 = Option.some(getAmazonClient());
      for (Path wireFormatPath : wireFormatPaths) {
        byte[] wireFormatBytes = Files.readAllBytes(wireFormatPath);
        String md5AsBase64 = Md5Utils.md5AsBase64(wireFormatBytes);

        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType("application/json");
        objectMetadata.setContentEncoding("gzip");
        objectMetadata.setContentLength(wireFormatBytes.length);
        objectMetadata.setContentMD5(md5AsBase64);

        maybeAmazonS3.ifDefined(amazonS3 -> amazonS3.putObject(
            WIRE_FORMAT_BUCKET_NAME,
            wireFormatPath.getFileName().toString(),
            new ByteArrayInputStream(wireFormatBytes),
            objectMetadata));

        Files.delete(wireFormatPath);
      }
    } catch (AmazonServiceException e) {
      throw new RuntimeException(String.format("s3 could not process upload: %s", e.getMessage()), e);
    } catch (SdkClientException e) {
      throw new RuntimeException(String.format("s3 could not be reached or bad response: %s", e.getMessage()), e);
    } finally {
      maybeAmazonS3.ifDefined(AmazonS3::shutdown);
    }
  }

  @VisibleForTesting
  AmazonS3 getAmazonClient() {
    return AmazonS3ClientBuilder.standard()
        .withRegion(Regions.US_WEST_2)
        .build();
  }

}
