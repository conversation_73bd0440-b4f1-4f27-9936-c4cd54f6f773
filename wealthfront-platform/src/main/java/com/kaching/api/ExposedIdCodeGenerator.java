package com.kaching.api;

import java.util.List;
import java.util.Set;

import com.google.inject.ImplementedBy;
import com.kaching.platform.common.Errors;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.hibernate.HibernateEntity;
import com.squareup.javapoet.JavaFile;

@ImplementedBy(ExposedIdCodeGeneratorImpl.class)
public interface ExposedIdCodeGenerator {

  List<JavaFile> generateProductionFiles(Errors errors, Class<? extends ServiceKind> serviceKind, Set<Class<? extends HibernateEntity>> entities);
  
  List<JavaFile> generateTestFiles(Errors errors, Class<? extends ServiceKind> serviceKind, Set<Class<? extends HibernateEntity>> entities);

}
