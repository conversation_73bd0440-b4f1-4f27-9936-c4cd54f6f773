package com.kaching.api;

import static com.google.common.base.Preconditions.checkArgument;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.google.common.base.Objects;
import com.google.common.collect.ImmutableList;
import com.google.inject.ImplementedBy;
import com.kaching.api.JavaApiType.JavaEnumType;
import com.kaching.api.JavaApiType.JavaObjectType;
import com.kaching.platform.discovery.ServiceKind;
import com.squareup.javapoet.ClassName;

@ImplementedBy(DefaultJavaApiTypeNamespaceTransformer.class)
public interface JavaApiTypeNamespaceTransformer {

  FullyQualifiedTypeName resolveQuery(Class<? extends ServiceKind> serviceKind, JavaApiQuerySchema query);

  FullyQualifiedTypeName resolveObject(Class<? extends ServiceKind> serviceKind, JavaApiQuerySchema query, JavaObjectType object);

  FullyQualifiedTypeName resolveEnum(Class<? extends ServiceKind> serviceKind, JavaApiQuerySchema query, JavaEnumType enumSchema);

  class FullyQualifiedTypeName {

    public final String packageName;
    public final List<String> nestedClassNames;

    public FullyQualifiedTypeName(Class<?> realClass) {
      this.packageName = getPackageName(realClass);
      this.nestedClassNames = Arrays.asList(realClass.getName().substring(this.packageName.length() + 1).split("[.$]"));
    }

   public FullyQualifiedTypeName(String packageName, Class<?> realClass) {
      this.packageName = packageName;
      this.nestedClassNames = List.of(realClass.getSimpleName());
    }

    public FullyQualifiedTypeName(String packageName, List<String> nestedClassNames) {
      checkArgument(!nestedClassNames.isEmpty(), "NewTypeName must at least have a top-level class");
      this.packageName = packageName;
      this.nestedClassNames = ImmutableList.copyOf(nestedClassNames);
    }

    public String getTopLevelName() {
      return nestedClassNames.get(0);
    }

    public String getSimpleName() {
      return nestedClassNames.get(nestedClassNames.size() - 1);
    }

    public boolean isTopLevel() {
      return nestedClassNames.size() == 1;
    }

    public String getFullyQualifiedName() {
      StringBuilder sb = new StringBuilder();
      sb.append(packageName);
      for (String clazz : nestedClassNames) {
        sb.append(".");
        sb.append(clazz);
      }
      return sb.toString();
    }

    public ClassName getJavaPoetClassName() {
      String[] innerClasses = nestedClassNames.stream()
          .skip(1)
          .toArray(String[]::new);
      return ClassName.get(packageName, getTopLevelName(), innerClasses);
    }

    public FullyQualifiedTypeName getInnerClass(String innerClassName) {
      List<String> classNames = new ArrayList<>(nestedClassNames);
      classNames.add(innerClassName);
      return new FullyQualifiedTypeName(packageName, classNames);
    }

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      FullyQualifiedTypeName that = (FullyQualifiedTypeName) o;
      return Objects.equal(packageName, that.packageName) &&
          Objects.equal(nestedClassNames, that.nestedClassNames);
    }

    @Override
    public int hashCode() {
      return Objects.hashCode(packageName, nestedClassNames);
    }

    @Override
    public String toString() {
      return getFullyQualifiedName();
    }

    private static String getPackageName(Class<?> clazz) {
      Class<?> c = clazz;
      while (c.isArray()) {
        c = c.getComponentType();
      }
      if (c.isPrimitive()) {
        return "java.lang";
      } else {
        String cn = c.getName();
        int dot = cn.lastIndexOf('.');
        return (dot != -1) ? cn.substring(0, dot).intern() : "";
      }
    }

  }

}
