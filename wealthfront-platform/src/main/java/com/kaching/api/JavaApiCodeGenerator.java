package com.kaching.api;

import java.util.List;

import com.google.inject.ImplementedBy;
import com.kaching.platform.common.Errors;
import com.kaching.platform.discovery.ServiceKind;
import com.squareup.javapoet.JavaFile;

@ImplementedBy(JavaApiCodeGeneratorImpl.class)
public interface JavaApiCodeGenerator {

  List<JavaFile> generateFiles(Errors errors, Class<? extends ServiceKind> serviceKind, JavaApiTypesBuilder allTypes, JavaApiTypeNamespaceTransformer namespaceTransformer);

  List<JavaFile> generateFilesFromJsonEntities(Errors errors, Class<? extends ServiceKind> serviceKind, JavaApiTypesBuilder allTypes, GlobalEntityNamespaceTransformer namespaceTransformer);

}
