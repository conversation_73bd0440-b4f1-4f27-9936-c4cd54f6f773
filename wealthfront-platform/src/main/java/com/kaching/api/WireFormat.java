package com.kaching.api;

import static com.google.common.base.Preconditions.checkArgument;
import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.kaching.api.WireFormatComparison.INCOMPATIBLE;
import static com.kaching.api.WireFormatComparison.LOOSER;
import static com.kaching.api.WireFormatComparison.STRICTER;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import com.kaching.platform.common.Strings;
import com.twolattes.json.Entity;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;

@Entity(
    discriminatorName = "type",
    subclasses = {
        WireFormat.QueryParameter.class,
        WireFormat.QueryParameterList.class,
        WireFormat.StringSerializable.class,
        WireFormat.Decimal.class,
        WireFormat.Integer.class,
        WireFormat.StringEnum.class,
        WireFormat.LocalDateTime.class,
        WireFormat.LocalDate.class,
        WireFormat.JsonValue.class,
        WireFormat.RecursiveStubJsonValue.class,
        WireFormat.JsonBoolean.class,
        WireFormat.JsonNumber.class,
        WireFormat.JsonString.class,
        WireFormat.JsonArray.class,
        WireFormat.JsonObject.class,
        WireFormat.JsonMap.class,
        WireFormat.FixedKeysJsonObject.class,
        WireFormat.PolymorphicFixedKeysJsonObject.class,
        WireFormat.TypedJsonTuple.class,
        WireFormat.ServiceQuery.class,
    }
)
public abstract class WireFormat {
  
  private static final Marshaller<WireFormat> MARSHALLER = createMarshaller(WireFormat.class);
  
  public record Comparison(Breadcrumbs breadcrumbs, WireFormatComparison comparison, String message) {
    
    public Comparison(Breadcrumbs breadcrumbs, WireFormatComparison comparison, String message, Object... args) {
      this(breadcrumbs, comparison, Strings.format(message, args));
    }
    
  }
  
  public final List<Comparison> compare(Breadcrumbs breadcrumbs, WireFormat newFormat) {
    if (this.getClass().equals(newFormat.getClass())) {
      return visit(new Visitor<List<Comparison>>() {
        @Override
        public List<Comparison> visitQueryParameter(QueryParameter queryParameter) {
          return queryParameter.compareSameClass(breadcrumbs, (QueryParameter) newFormat);
        }

        @Override
        public List<Comparison> visitQueryParameterList(QueryParameterList queryParameterList) {
          return queryParameterList.compareSameClass(breadcrumbs, (QueryParameterList) newFormat);
        }

        @Override
        public List<Comparison> visitStringSerializable(StringSerializable stringSerializable) {
          return stringSerializable.compareSameClass(breadcrumbs, (StringSerializable) newFormat);
        }

        @Override
        public List<Comparison> visitJsonMap(JsonMap jsonMap) {
          return jsonMap.compareSameClass(breadcrumbs, (JsonMap) newFormat);
        }

        @Override
        public List<Comparison> visitFixedKeysJsonObjectFormat(FixedKeysJsonObject fixedKeysJsonObjectFormat) {
          return fixedKeysJsonObjectFormat.compareSameClass(breadcrumbs, (FixedKeysJsonObject) newFormat);
        }

        @Override
        public List<Comparison> visitPolymorphicFixedKeysJsonObject(
            PolymorphicFixedKeysJsonObject polymorphicFixedKeysJsonObject) {
          return polymorphicFixedKeysJsonObject.compareSameClass(breadcrumbs, (PolymorphicFixedKeysJsonObject) newFormat);
        }

        @Override
        public List<Comparison> visitJsonValue(JsonValue jsonValue) {
          return jsonValue.compareSameClass(breadcrumbs, (JsonValue) newFormat);
        }

        @Override
        public List<Comparison> visitRecursionStubJsonValue(RecursiveStubJsonValue recursiveStubJsonValue) {
          return recursiveStubJsonValue.compareSameClass(breadcrumbs, (RecursiveStubJsonValue) newFormat);
        }

        @Override
        public List<Comparison> visitJsonObject(JsonObject jsonObject) {
          return jsonObject.compareSameClass(breadcrumbs, (JsonObject) newFormat);
        }

        @Override
        public List<Comparison> visitJsonArray(JsonArray jsonArray) {
          return jsonArray.compareSameClass(breadcrumbs, (JsonArray) newFormat);
        }

        @Override
        public List<Comparison> visitJsonBoolean(JsonBoolean jsonBoolean) {
          return jsonBoolean.compareSameClass(breadcrumbs, (JsonBoolean) newFormat);
        }

        @Override
        public List<Comparison> visitJsonNumber(JsonNumber jsonNumber) {
          return jsonNumber.compareSameClass(breadcrumbs, (JsonNumber) newFormat);
        }

        @Override
        public List<Comparison> visitJsonString(JsonString jsonString) {
          return jsonString.compareSameClass(breadcrumbs, (JsonString) newFormat);
        }

        @Override
        public List<Comparison> visitStringEnum(StringEnum stringEnum) {
          return stringEnum.compareSameClass(breadcrumbs, (StringEnum) newFormat);
        }

        @Override
        public List<Comparison> visitLocalDateTime(LocalDateTime localDateTime) {
          return localDateTime.compareSameClass(breadcrumbs, (LocalDateTime) newFormat);
        }

        @Override
        public List<Comparison> visitLocalDate(LocalDate localDate) {
          return localDate.compareSameClass(breadcrumbs, (LocalDate) newFormat);
        }

        @Override
        public List<Comparison> visitTypedJsonTuple(TypedJsonTuple typedJsonTuple) {
          return typedJsonTuple.compareSameClass(breadcrumbs, (TypedJsonTuple) newFormat);
        }

        @Override
        public List<Comparison> visitDecimal(Decimal decimal) {
          return decimal.compareSameClass(breadcrumbs, (Decimal) newFormat);
        }

        @Override
        public List<Comparison> visitInteger(Integer integer) {
          return integer.compareSameClass(breadcrumbs, (Integer) newFormat);
        }

        @Override
        public List<Comparison> visitServiceQuery(ServiceQuery serviceQuery) {
          return serviceQuery.compareSameClass(breadcrumbs, (ServiceQuery) newFormat);
        }
      });
    } else if (this.getClass().isAssignableFrom(newFormat.getClass())) {
      return List.of(new Comparison(breadcrumbs, STRICTER, "New format of type %s is stricter than old format of type %s", newFormat.getClass().getSimpleName(), getClass().getSimpleName())); 
    } else if (newFormat.getClass().isAssignableFrom(this.getClass())) {
      return List.of(new Comparison(breadcrumbs, LOOSER, "New format of type %s is looser than old format of type %s", newFormat.getClass().getSimpleName(), getClass().getSimpleName()));
    } else {
      return List.of(new Comparison(breadcrumbs, INCOMPATIBLE, "New format of type %s is incompatible with old format of type %s", newFormat.getClass().getSimpleName(), getClass().getSimpleName()));
    }
  }
  
  public abstract <T> T visit(Visitor<T> visitor);

  @Override
  public boolean equals(Object obj) {
    if (obj == null) {
      return false;
    }
    if (obj instanceof WireFormat other) {
      return MARSHALLER.marshall(this).equals(MARSHALLER.marshall(other));
    }
    return false;
  }

  @Override
  public int hashCode() {
    return MARSHALLER.marshall(this).hashCode();
  }
  
  @Override
  public String toString() {
    return MARSHALLER.marshall(this).toPrettyPrintString();
  }

  @Entity(discriminator = "service-query")
  public static class ServiceQuery extends WireFormat {

    @Value(nullable = false) private String queryName;
    @Value(nullable = false) private WireFormat returnFormat;
    @Value(nullable = false) private WireFormat parameterFormat;

    private ServiceQuery() { /* JSON */ }

    public ServiceQuery(String queryName, StringSerializable returnFormat, QueryParameterList parameterFormat) {
      this.queryName = queryName;
      this.returnFormat = returnFormat;
      this.parameterFormat = parameterFormat;
    }

    public String getQueryName() {
      return queryName;
    }

    public StringSerializable getReturnType() {
      return (StringSerializable) returnFormat;
    }

    public QueryParameterList getParameterFormat() {
      return (QueryParameterList) parameterFormat;
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitServiceQuery(this);
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, ServiceQuery newQuery) {
      Breadcrumbs queryCrumbs = breadcrumbs.push(queryName);
      List<Comparison> result = new ArrayList<>();
      result.addAll(returnFormat.compare(queryCrumbs.push("return"), newQuery.returnFormat));
      result.addAll(parameterFormat.compare(queryCrumbs.push("parameters"), newQuery.parameterFormat));
      return result;
    }
  }
  
  @Entity(discriminator = "query-parameter")
  public static class QueryParameter extends WireFormat {
    
    @Value(nullable = false) private boolean optional;
    @Value(nullable = false) private WireFormat parameterType;
    
    private QueryParameter() { /* JSON */ }

    public QueryParameter(boolean optional, StringSerializable parameterType) {
      this.optional = optional;
      this.parameterType = parameterType;
    }

    public boolean isOptional() {
      return optional;
    }

    public StringSerializable getParameterType() {
      return (StringSerializable) parameterType;
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitQueryParameter(this);
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, QueryParameter newParam) {
      List<Comparison> result = new ArrayList<>();
      if (optional && !newParam.optional) {
        result.add(new Comparison(breadcrumbs, STRICTER, "Query param made non-optional"));
      }
      if (!optional && newParam.optional) {
        result.add(new Comparison(breadcrumbs, LOOSER, "Query param made optional. Must verify all producers of this param (callers) exclusively produce non-null values"));
      }
      result.addAll(parameterType.compare(breadcrumbs, newParam.parameterType));
      return result;
    }
    
  }
  
  @Entity(discriminator = "query-parameter-list")
  public static class QueryParameterList extends WireFormat {
    
    @Value(nullable = false) private List<QueryParameter> parameters;
    
    private QueryParameterList() { /* JSON */ }

    public QueryParameterList(List<QueryParameter> parameters) {
      this.parameters = parameters;
    }

    public List<QueryParameter> getParameters() {
      return parameters;
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitQueryParameterList(this);
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, QueryParameterList newList) {
      List<Comparison> result = new ArrayList<>();
      int maxLength = Math.max(newList.parameters.size(), parameters.size());
      for (int i = 0; i < maxLength; i++) {
        Breadcrumbs paramCrumbs = breadcrumbs.push("Param" + i);
        if (i < newList.parameters.size() && i < parameters.size()) {
          QueryParameter thisFormat = parameters.get(i);
          QueryParameter otherFormat = newList.parameters.get(i);
          result.addAll(thisFormat.compareSameClass(paramCrumbs, otherFormat));
        } else if (i < parameters.size()) {
          if (parameters.get(i).isOptional()) {
            result.add(new Comparison(paramCrumbs, STRICTER, "Removing an optional query parameter"));
          } else {
            result.add(new Comparison(paramCrumbs, INCOMPATIBLE, "Removing a non-optional query parameter"));
          }
        } else {
          if (newList.parameters.get(i).isOptional()) {
            result.add(new Comparison(paramCrumbs, LOOSER, "Adding an optional query parameter"));
          } else {
            result.add(new Comparison(paramCrumbs, INCOMPATIBLE, "Adding a non-optional query parameter"));
          }
        }
      }
      return result;
    }
    
  }  

  @Entity(discriminator = "string-serializable")
  public static class StringSerializable extends WireFormat {

    public StringSerializable() {}

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitStringSerializable(this);
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, StringSerializable newFormat) {
      return List.of();
    }
    
  }
  
  @Entity(discriminator = "decimal")
  public static class Decimal extends StringSerializable {

    public Decimal() {}

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, Decimal newFormat) {
      return List.of();
    }
    
  }

  @Entity(discriminator = "integer")
  public static class Integer extends Decimal {

    public Integer() {}

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, Integer newFormat) {
      return List.of();
    }

  }

  @Entity(discriminator = "string-enum")
  public static class StringEnum extends StringSerializable {

    @Value(nullable = false) private Set<String> values;

    private StringEnum() { /* JSON */ }

    public StringEnum(Set<String> values) {
      this.values = values;
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, StringEnum newFormat) {
      List<Comparison> result = new ArrayList<>();
      List<String> newValues = ImmutableList.sortedCopyOf(Sets.difference(newFormat.values, values));
      if (!newValues.isEmpty()) {
        result.add(new Comparison(breadcrumbs, LOOSER, "New enum values: %s", newValues));
      }
      List<String> removedValues = ImmutableList.sortedCopyOf(Sets.difference(values, newFormat.values));
      if (!removedValues.isEmpty()) {
        result.add(new Comparison(breadcrumbs, STRICTER, "Removed enum values: %s", removedValues));
      }
      return result;
    }

    public Set<String> getValues() {
      return values;
    }

    public static StringEnum create(String... enumValues) {
      return new StringEnum(new HashSet<>(Arrays.asList(enumValues)));
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitStringEnum(this);
    }

  }

  @Entity(discriminator = "local-date-time")
  public static class LocalDateTime extends StringSerializable {

    public LocalDateTime() {}

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, LocalDateTime newFormat) {
      return List.of();
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitLocalDateTime(this);
    }

  }

  @Entity(discriminator = "local-date")
  public static class LocalDate extends LocalDateTime {

    public LocalDate() {}

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, LocalDate newFormat) {
      return List.of();
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitLocalDate(this);
    }

  }
  
  @Entity(discriminator = "json-value")
  public static class JsonValue extends StringSerializable {

    public JsonValue() {}

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitJsonValue(this);
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, JsonValue newFormat) {
      return List.of();
    }
    
  }

  @Entity(discriminator = "recursive-stub-json-value")
  public static class RecursiveStubJsonValue extends JsonValue {

    @Value(nullable = false) private String recursiveJavaEntityName;

    private RecursiveStubJsonValue() { /* JSON */ }

    public RecursiveStubJsonValue(String recursiveJavaEntityName) {
      this.recursiveJavaEntityName = recursiveJavaEntityName;
    }

    public String getRecursiveJavaEntityName() {
      return recursiveJavaEntityName;
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitRecursionStubJsonValue(this);
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, RecursiveStubJsonValue newFormat) {
      return List.of();
    }

  }

  @Entity(discriminator = "json-boolean")
  public static class JsonBoolean extends JsonValue {

    public JsonBoolean() {}

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, JsonBoolean newFormat) {
      checkArgument(newFormat.getClass().equals(JsonBoolean.class));
      return List.of();
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitJsonBoolean(this);
    }
  }
  
  @Entity(discriminator = "json-number")
  public static class JsonNumber extends JsonValue {

    @Value(nullable = false) private Decimal format;
    
    private JsonNumber() { /* JSON */ }

    public JsonNumber(Decimal format) {
      this.format = format;
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, JsonNumber newFormat) {
      return format.compare(breadcrumbs, newFormat.format);
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitJsonNumber(this);
    }

  }

  @Entity(discriminator = "json-string")
  public static class JsonString extends JsonValue {

    @Value(nullable = false) private WireFormat format;

    private JsonString() { /* JSON */ }

    public JsonString(StringSerializable format) {
      this.format = format;
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, JsonString newFormat) {
      return format.compare(breadcrumbs, newFormat.format);
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitJsonString(this);
    }

    public StringSerializable getFormat() {
      return (StringSerializable) format;
    }

  }

  @Entity(discriminator = "json-array")
  public static class JsonArray extends JsonValue {
    
    @Value private WireFormat valueFormat;

    private JsonArray() { /* JSON */ }

    public JsonArray(JsonValue valueFormat) {
      this.valueFormat = valueFormat;
    }

    public JsonValue getValueFormat() {
      return (JsonValue) valueFormat;
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, JsonArray newFormat) {
      return valueFormat.compare(breadcrumbs, newFormat.valueFormat);
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitJsonArray(this);
    }

  }
  
  @Entity(discriminator = "json-object")
  public static class JsonObject extends JsonValue {

    public JsonObject() {}

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, JsonObject newFormat) {
      return List.of();
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitJsonObject(this);
    }

  }
  
  @Entity(discriminator = "json-map")
  public static class JsonMap extends JsonObject {
    
    @Value(nullable = false) private WireFormat keyFormat;
    @Value(nullable = false) private WireFormat valueFormat;
    
    private JsonMap() { /* JSON */ }
    
    public JsonMap(JsonString keyFormat, JsonValue valueFormat) {
      this.keyFormat = keyFormat;
      this.valueFormat = valueFormat;
    }

    public JsonString getKeyFormat() {
      return (JsonString) keyFormat;
    }

    public JsonValue getValueFormat() {
      return (JsonValue) valueFormat;
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, JsonMap newFormat) {
      List<Comparison> result = new ArrayList<>();
      result.addAll(keyFormat.compare(breadcrumbs.push("key"), newFormat.keyFormat));
      result.addAll(valueFormat.compare(breadcrumbs.push("value"), newFormat.valueFormat));
      return result;
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitJsonMap(this);
    }

  }
  
  @Entity
  public static class JsonObjectField {

    @Value(nullable = false) private boolean optional;
    @Value(nullable = false) private boolean nullable;
    @Value(nullable = false) private WireFormat format;

    private JsonObjectField() { /* JSON */ }

    public JsonObjectField(boolean optional, boolean nullable, JsonValue format) {
      this.optional = optional;
      this.nullable = nullable;
      this.format = format;
    }

    public boolean isOptional() {
      return optional;
    }

    public boolean isNullable() {
      return nullable;
    }

    public JsonValue getFormat() {
      return (JsonValue) format;
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, JsonObjectField newField) {
      List<Comparison> result = new ArrayList<>();
      if (this.optional && !newField.optional) {
        result.add(new Comparison(breadcrumbs, STRICTER, "Made field non-optional"));
      }
      if (!this.optional && newField.optional) {
        result.add(new Comparison(breadcrumbs, LOOSER, "Made optional. Must verify all producers of this field are non-null"));
      }
      if (this.nullable && !newField.nullable) {
        result.add(new Comparison(breadcrumbs, STRICTER, "Made field non-nullable"));
      }
      if (!this.nullable && newField.nullable) {
        result.add(new Comparison(breadcrumbs, LOOSER, "Made field nullable"));
      }
      result.addAll(format.compare(breadcrumbs, newField.format));
      return result;
    }

  }

  @Entity(discriminator = "fixed-keys-json-object")
  public static class FixedKeysJsonObject extends JsonObject {

    @Value(nullable = false) private Map<String, JsonObjectField> fields;
    
    private FixedKeysJsonObject() { /* JSON */ }

    public FixedKeysJsonObject(Map<String, JsonObjectField> fields) {
      this.fields = fields;
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, FixedKeysJsonObject newObject) {
      List<Comparison> result = new ArrayList<>();
      for (String key : ImmutableList.sortedCopyOf(Sets.union(newObject.fields.keySet(), fields.keySet()))) {
        JsonObjectField thisField = this.fields.get(key);
        JsonObjectField otherField = newObject.fields.get(key);
        Breadcrumbs fieldCrumbs = breadcrumbs.push(key);
        if (thisField != null && otherField != null) {
          result.addAll(thisField.compareSameClass(breadcrumbs, otherField));
        } else if (thisField == null) {
          if (otherField.isOptional()) {
            result.add(new Comparison(fieldCrumbs, LOOSER, "Adding optional field. OK to start producing simultaneously"));
          } else {
            result.add(new Comparison(fieldCrumbs, INCOMPATIBLE, "Adding non-optional field"));
          }
        } else {
          if (thisField.isOptional()) {
            result.add(new Comparison(fieldCrumbs, STRICTER, "Removing optional field"));
          } else {
            result.add(new Comparison(fieldCrumbs, INCOMPATIBLE, "Removing non-optional field"));
          }
        }
      }
      return result;
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitFixedKeysJsonObjectFormat(this);
    }

    public Map<String, JsonObjectField> getFields() {
      return ImmutableMap.copyOf(fields);
    }
    
    public FixedKeysJsonObject withField(String name, boolean optional, boolean nullable, JsonValue format) {
      fields.put(name, new JsonObjectField(optional, nullable, format));
      return this;
    }

  }
  
  @Entity(discriminator = "polymorphic-fixed-keys-json-object")
  public static class PolymorphicFixedKeysJsonObject extends JsonObject {
    
    @Value(nullable = false) private String discriminatorName;
    @Value(nullable = false) private Map<String, FixedKeysJsonObject> fieldsByDiscriminatorValue;

    private PolymorphicFixedKeysJsonObject() { /* JSON */ }
    
    public PolymorphicFixedKeysJsonObject(String discriminatorName,
                                          Map<String, FixedKeysJsonObject> fieldsByDiscriminatorValue) {
      this.discriminatorName = discriminatorName;
      this.fieldsByDiscriminatorValue = fieldsByDiscriminatorValue;
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, PolymorphicFixedKeysJsonObject newObject) {
      List<Comparison> result = new ArrayList<>();
      if (!newObject.discriminatorName.equals(discriminatorName)) {
        result.add(new Comparison(breadcrumbs, INCOMPATIBLE, "Discriminator name changed from %s to %s", discriminatorName, newObject.discriminatorName));
      }
      for (String discriminator : ImmutableList.sortedCopyOf(Sets.union(fieldsByDiscriminatorValue.keySet(), newObject.fieldsByDiscriminatorValue.keySet()))) {
        Breadcrumbs subTypeCrumbs = breadcrumbs.push(discriminator);
        FixedKeysJsonObject thisObject = fieldsByDiscriminatorValue.get(discriminator);
        FixedKeysJsonObject otherObject = newObject.fieldsByDiscriminatorValue.get(discriminator);
        if (thisObject != null && otherObject != null) {
          result.addAll(thisObject.compareSameClass(subTypeCrumbs, otherObject));
        } else if (thisObject == null) {
          result.add(new Comparison(subTypeCrumbs, LOOSER, "Adding new sub-type: %s", discriminator));
        } else {
          result.add(new Comparison(subTypeCrumbs, STRICTER, "Removing sub-type: %s", discriminator));
        }
      }
      return result;
    }

    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitPolymorphicFixedKeysJsonObject(this);
    }

    public String getDiscriminatorName() {
      return discriminatorName;
    }

    public Map<String, FixedKeysJsonObject> getFieldsByDiscriminatorValue() {
      return ImmutableMap.copyOf(fieldsByDiscriminatorValue);
    }

    public static PolymorphicFixedKeysJsonObject create(String discriminatorName) {
      return new PolymorphicFixedKeysJsonObject(discriminatorName, new HashMap<>());
    }
    
    public PolymorphicFixedKeysJsonObject withField(String discriminator, String fieldName, boolean optional, boolean nullable, JsonValue format) {
      FixedKeysJsonObject object = fieldsByDiscriminatorValue.computeIfAbsent(discriminator, key -> new FixedKeysJsonObject(new HashMap<>()));
      object.withField(fieldName, optional, nullable, format);
      return this;
    }

    public PolymorphicFixedKeysJsonObject withField(Set<String> discriminators, String fieldName, boolean optional, boolean nullable, JsonValue format) {
      discriminators.forEach(discriminator -> withField(discriminator, fieldName, optional, nullable, format));
      return this;
    }
    
  }

  @Entity(discriminator = "typed-json-tuple")
  public static class TypedJsonTuple extends JsonArray {

    @Value(nullable = false) private List<JsonObjectField> fields;

    private TypedJsonTuple() { /* JSON */ }

    public TypedJsonTuple(List<JsonObjectField> fields) {
      this.fields = fields;
    }

    public List<JsonObjectField> getFields() {
      return fields;
    }

    private List<Comparison> compareSameClass(Breadcrumbs breadcrumbs, TypedJsonTuple newTuple) {
      int maxLength = Math.max(fields.size(), newTuple.fields.size());
      List<Comparison> result = new ArrayList<>();
      for (int i = 0; i < maxLength; i++) {
        JsonObjectField thisField = i < fields.size() ? fields.get(i) : null;
        JsonObjectField otherField = i < newTuple.fields.size() ? newTuple.fields.get(i) : null;
        Breadcrumbs fieldCrumbs = breadcrumbs.push(String.valueOf(i));
        if (thisField != null && otherField != null) {
          result.addAll(thisField.compareSameClass(fieldCrumbs, otherField));
        } else if (thisField == null) {
          if (otherField.isOptional()) {
            result.add(new Comparison(fieldCrumbs, LOOSER, "Adding optional field"));
          } else {
            result.add(new Comparison(fieldCrumbs, INCOMPATIBLE, "Adding non-optional field"));
          }
        } else {
          if (thisField.isOptional()) {
            result.add(new Comparison(fieldCrumbs, STRICTER, "Removing optional field"));
          } else {
            result.add(new Comparison(fieldCrumbs, INCOMPATIBLE, "Removing non-optional field"));
          }
        }
      }
      return result;
    }
    
    @Override
    public <T> T visit(Visitor<T> visitor) {
      return visitor.visitTypedJsonTuple(this);
    }

  }

  public static class Breadcrumbs {

    private final List<String> pieces;

    public Breadcrumbs() {
      this.pieces = List.of();
    }

    public Breadcrumbs(String... pieces) {
      this.pieces = ImmutableList.copyOf(pieces);
    }

    private Breadcrumbs(List<String> pieces) {
      this.pieces = pieces;
    }

    public Breadcrumbs push(String piece) {
      ImmutableList.Builder<String> builder = ImmutableList.builderWithExpectedSize(pieces.size() + 1);
      builder.addAll(pieces);
      builder.add(piece);
      return new Breadcrumbs(builder.build());
    }

    @Override
    public String toString() {
      return String.join(".", pieces);
    }

    @Override
    public boolean equals(Object obj) {
      if (obj == this) {
        return true;
      }
      if (obj instanceof Breadcrumbs crumbs) {
        return pieces.equals(crumbs.pieces);
      }
      return false;
    }

    @Override
    public int hashCode() {
      return pieces.hashCode();
    }

  }
  
  public interface Visitor<T> {
    
    T visitQueryParameter(QueryParameter queryParameter);
    
    T visitQueryParameterList(QueryParameterList queryParameterList);
    
    T visitStringSerializable(StringSerializable stringSerializable);

    T visitDecimal(Decimal decimal);

    T visitInteger(Integer integer);

    T visitJsonMap(JsonMap jsonMap);
    
    T visitFixedKeysJsonObjectFormat(FixedKeysJsonObject fixedKeysJsonObjectFormat);

    T visitPolymorphicFixedKeysJsonObject(PolymorphicFixedKeysJsonObject polymorphicFixedKeysJsonObject);

    T visitJsonValue(JsonValue jsonValue);

    T visitRecursionStubJsonValue(RecursiveStubJsonValue recursiveStubJsonValue);

    T visitJsonObject(JsonObject jsonObject);

    T visitJsonArray(JsonArray jsonArray);

    T visitJsonBoolean(JsonBoolean jsonBoolean);

    T visitJsonNumber(JsonNumber jsonNumber);

    T visitJsonString(JsonString jsonString);

    T visitStringEnum(StringEnum stringEnum);

    T visitLocalDateTime(LocalDateTime localDateTime);

    T visitLocalDate(LocalDate localDate);

    T visitTypedJsonTuple(TypedJsonTuple typedJsonTuple);

    T visitServiceQuery(ServiceQuery serviceQuery);
  }

}
