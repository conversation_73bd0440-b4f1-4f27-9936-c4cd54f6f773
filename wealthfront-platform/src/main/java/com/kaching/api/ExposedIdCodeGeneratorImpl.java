package com.kaching.api;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkState;
import static java.util.stream.Collectors.toList;
import static javax.lang.model.element.Modifier.PUBLIC;
import static javax.lang.model.element.Modifier.STATIC;

import java.lang.reflect.Constructor;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.CaseFormat;
import com.google.common.collect.ImmutableList;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.name.Named;
import com.google.inject.util.Types;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.Identifier;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.hibernate.HibernateEntity;
import com.kaching.util.id.LongIdExternalizer;
import com.squareup.javapoet.AnnotationSpec;
import com.squareup.javapoet.ClassName;
import com.squareup.javapoet.CodeBlock;
import com.squareup.javapoet.JavaFile;
import com.squareup.javapoet.MethodSpec;
import com.squareup.javapoet.ParameterSpec;
import com.squareup.javapoet.TypeSpec;

public class ExposedIdCodeGeneratorImpl implements ExposedIdCodeGenerator {
  
  private static final String PACKAGE = "com.wealthfront.auto.id";
  
  @Override
  public List<JavaFile> generateProductionFiles(Errors errors, Class<? extends ServiceKind> serviceKind, Set<Class<? extends HibernateEntity>> entities) {
    List<ExposeId> annotations = validateAndGetAnnotations(errors, entities);
    if (annotations.isEmpty()) {
      return Collections.emptyList();
    }
    String serviceKindLower = serviceKind.getSimpleName().toLowerCase();
    String className = StringUtils.capitalize(serviceKindLower) + "ClientExternalizerModule";

    JavaFile clientExternalizerModule = JavaFile.builder(PACKAGE, TypeSpec.classBuilder(className)
            .addModifiers(PUBLIC)
            .superclass(AbstractModule.class)
            .addMethods(annotations.stream()
                .map(this::generateProdModuleMethod)
                .collect(toList())
            ).build())
        .skipJavaLangImports(true)
        .indent("  ")
        .build();
    
    return ImmutableList.of(clientExternalizerModule);
  }

  @Override
  public List<JavaFile> generateTestFiles(Errors errors, Class<? extends ServiceKind> serviceKind, Set<Class<? extends HibernateEntity>> entities) {
    List<ExposeId> annotations = validateAndGetAnnotations(errors, entities);
    if (annotations.isEmpty()) {
      return Collections.emptyList();
    }
    String serviceKindLower = serviceKind.getSimpleName().toLowerCase();
    
    String packageName = String.format(PACKAGE, serviceKindLower);
    ClassName moduleClassName = ClassName.get(packageName, "Fake" + StringUtils.capitalize(serviceKindLower) + "ExternalizerModule");
    JavaFile fakeClientExternalizerModule = JavaFile.builder(packageName, TypeSpec.classBuilder(moduleClassName)
            .addModifiers(PUBLIC)
            .superclass(AbstractModule.class)
            .addMethods(annotations.stream()
                .map(annotation -> generateFakeModuleMethods(annotation, moduleClassName))
                .flatMap(List::stream)
                .collect(toList())
            ).build())
        .skipJavaLangImports(true)
        .indent("  ")
        .build();
    
    return ImmutableList.of(fakeClientExternalizerModule);
  }

  private MethodSpec generateProdModuleMethod(ExposeId annotation) {
    return MethodSpec.methodBuilder("get" + annotation.exposedIdClass().getSimpleName() + "Externalizer")
        .addAnnotation(Provides.class)
        .addAnnotation(Singleton.class)
        .returns(Types.newParameterizedType(LongIdExternalizer.class, annotation.exposedIdClass()))
        .addParameter(ParameterSpec.builder(String.class, "password")
            .addAnnotation(AnnotationSpec.builder(Named.class)
                .addMember("value", CodeBlock.builder().add("$S", annotation.passwordProperty()).build())
                .build())
            .build()
        ).addStatement(CodeBlock.builder()
            .add("return new LongIdExternalizer<>($T.class, $S, password)", annotation.exposedIdClass(), annotation.prefix())
            .build())
        .build();
  }

  private List<MethodSpec> generateFakeModuleMethods(ExposeId annotation, ClassName moduleClassName) {
    String[] entityNameParts = CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, annotation.exposedIdClass().getSimpleName()).split("_");
    List<String> fakePasswordParts = removeLastElement(Arrays.asList(entityNameParts));
    fakePasswordParts.add("password");
    String fakePassword = String.join(" ", fakePasswordParts);

    String providerMethodName = "get" + annotation.exposedIdClass().getSimpleName() + "Externalizer";
    MethodSpec providerMethod = MethodSpec.methodBuilder(providerMethodName)
        .addAnnotation(Provides.class)
        .addAnnotation(Singleton.class)
        .returns(Types.newParameterizedType(LongIdExternalizer.class, annotation.exposedIdClass()))
        .addStatement(CodeBlock.builder()
            .add("return new LongIdExternalizer<>($T.class, $S, $S)", annotation.exposedIdClass(), annotation.prefix(), fakePassword)
            .build())
        .build();
    
    MethodSpec staticMethod = MethodSpec.methodBuilder(StringUtils.uncapitalize(annotation.exposedIdClass().getSimpleName()) + "Externalizer")
        .addModifiers(PUBLIC, STATIC)
        .returns(Types.newParameterizedType(LongIdExternalizer.class, annotation.exposedIdClass()))
        .addStatement("return new $T().$L()", moduleClassName, providerMethodName)
        .build();
    return ImmutableList.of(providerMethod, staticMethod);
  }
  
  private List<ExposeId> validateAndGetAnnotations(Errors errors, Set<Class<? extends HibernateEntity>> entities) {
    Set<String> simpleNames = new HashSet<>();
    return entities.stream()
        .filter(entity -> entity.isAnnotationPresent(ExposeId.class))
        .filter(entity -> !entity.getAnnotation(ExposeId.class).prefix().equals(""))
        .map(entity -> {
          String entityName = entity.getSimpleName();
          if (simpleNames.contains(entityName)) {
            errors.addMessage("Name collision: two hibernate entities with @ExposeId and same name: %s", entityName);
          } else {
            simpleNames.add(entityName);
          }
          ExposeId annotation = entity.getAnnotation(ExposeId.class);
          if (!(entityName + "Id").equals(annotation.exposedIdClass().getSimpleName())) {
            errors.addMessage("@ExposeId expects the identifier for %s to be named %sId. Was %s", entityName, entityName, 
                annotation.exposedIdClass().getSimpleName());
          }
          try {
            validateLongIdConstructor(annotation.exposedIdClass());
          } catch (Exception ex) {
            errors.addMessage("Id class %s in @ExposeId must have a public constructor taking a single long value which is consistent with getId()",
                annotation.exposedIdClass().getSimpleName());
          }
          if (annotation.prefix().equals("") != annotation.passwordProperty().equals("")) {
            errors.addMessage("On @ExposeId(%s), prefix() and passwordProperty() should either both be set or both be left empty", 
                entityName);
          }
          return annotation;
        }).sorted(Comparator.comparing(annotation -> annotation.exposedIdClass().getSimpleName()))
        .collect(toList());
  }
  
  private void validateLongIdConstructor(Class<? extends Identifier<Long>> clazz) throws Exception {
    long sentinelValue = 123_456_789L;
    Constructor<? extends Identifier<Long>> declaredConstructor = clazz.getDeclaredConstructor(long.class);
    checkArgument(Modifier.isPublic(declaredConstructor.getModifiers()));
    Identifier<Long> identifier = declaredConstructor.newInstance(sentinelValue);
    checkState(identifier.getId() == sentinelValue);
  }

  private static <T> List<T> removeLastElement(List<T> input) {
    checkArgument(input.size() > 0, "Empty list given");
    List<T> output = new ArrayList<>(input.size() - 1);
    for (int i = 0; i < input.size() - 1; i++) {
      output.add(input.get(i));
    }
    return output;
  }
  
}
