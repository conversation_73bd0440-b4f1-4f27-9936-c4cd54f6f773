package com.kaching.api;

import static java.util.stream.Collectors.toList;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import com.kaching.api.WireFormat.QueryParameter;
import com.kaching.api.WireFormat.QueryParameterList;
import com.kaching.api.WireFormat.ServiceQuery;
import com.kaching.api.WireFormat.StringSerializable;
import com.kaching.platform.queryengine.Query;

public class JavaApiQuerySchema {
  
  private final Class<? extends Query<?>> queryClass;
  private final String queryName;
  private final List<JavaQueryParameter> parameters;
  private final JavaApiType returnType;

  public JavaApiQuerySchema(Class<? extends Query<?>> queryClass, List<JavaQueryParameter> parameters, JavaApiType returnType) {
    this.queryClass = queryClass;
    this.queryName = queryClass.getSimpleName();
    this.parameters = parameters;
    this.returnType = returnType;
  }

  public Class<? extends Query<?>> getQueryClass() {
    return queryClass;
  }

  public String getQueryName() {
    return queryName;
  }

  public List<JavaQueryParameter> getParameters() {
    return parameters;
  }

  public JavaApiType getReturnType() {
    return returnType;
  }

  public ServiceQuery getWireFormat() {
    return new ServiceQuery(queryName, getReturnTypeWireFormat(), getParametersWireFormat());
  }
  
  public QueryParameterList getParametersWireFormat() {
    List<QueryParameter> parameters = getParameters().stream()
        .map(p -> new QueryParameter(p.isOptional(), (StringSerializable) p.getType().getWireFormat()))
        .collect(toList());
    return new QueryParameterList(parameters);
  }
  
  public WireFormat.StringSerializable getReturnTypeWireFormat() {
    return (WireFormat.StringSerializable) returnType.getWireFormat();
  }

  Set<JavaApiType> getAllReferencedTypes() {
    Set<JavaApiType> result = new HashSet<>(returnType.getAllReferencedTypes());
    getParameters().forEach(param -> result.addAll(param.getType().getAllReferencedTypes()));
    return result;
  }

  public static class JavaQueryParameter {
    
    public enum Flag {
      SECURE_FIELD {
        @Override
        public <T> T visit(FlagVisitor<T> visitor) {
          return visitor.caseSecureField();
        }
      },
      OWNED_ENFORCING {
        @Override
        public <T> T visit(FlagVisitor<T> visitor) {
          return visitor.caseOwnedEnforcing();
        }
      },
      OPTIONAL {
        @Override
        public <T> T visit(FlagVisitor<T> visitor) {
          return visitor.caseOptional();
        }
      };
      
      public abstract  <T> T visit(FlagVisitor<T> visitor);
      
    }
    
    public interface FlagVisitor<T> {
      
      T caseSecureField();
      
      T caseOwnedEnforcing();
      
      T caseOptional();
    }
    
    private final String name;
    private final JavaApiType type;
    private final Set<Flag> flags;

    public JavaQueryParameter(String name, JavaApiType type, Set<Flag> flags) {
      this.name = name;
      this.type = type;
      this.flags = flags;
    }

    public String getName() {
      return name;
    }

    public JavaApiType getType() {
      return type;
    }

    public Set<Flag> getFlags() {
      return flags;
    }

    public boolean isOptional() {
      return flags.contains(Flag.OPTIONAL);
    }

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      JavaQueryParameter that = (JavaQueryParameter) o;
      return Objects.equal(name, that.name) && Objects.equal(type, that.type) &&
          Objects.equal(flags, that.flags);
    }

    @Override
    public int hashCode() {
      return Objects.hashCode(name, type, flags);
    }

    @Override
    public String toString() {
      return MoreObjects.toStringHelper(this)
          .add("name", name)
          .add("type", type)
          .add("flags", flags)
          .toString();
    }
    
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    JavaApiQuerySchema that = (JavaApiQuerySchema) o;
    return Objects.equal(queryClass, that.queryClass) && Objects.equal(queryName, that.queryName) &&
        Objects.equal(parameters, that.parameters) && Objects.equal(returnType, that.returnType);
  }

  @Override
  public int hashCode() {
    return Objects.hashCode(queryClass, queryName, parameters, returnType);
  }
  
}
