package com.kaching.api;

import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.kaching.util.GZipper.gzip;
import static java.util.logging.Logger.getLogger;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.FileSystem;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;
import java.util.stream.Stream;

import org.apache.commons.io.FilenameUtils;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.inject.Guice;
import com.google.inject.Inject;
import com.google.inject.Stage;
import com.kaching.platform.common.Errors;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.queryengine.ComponentInfoAggregator;
import com.kaching.platform.queryengine.Query;
import com.kaching.util.functional.Tuple2;
import com.twolattes.json.Marshaller;

public class WireFormatGenerator {

  private static final Logger LOG = getLogger(WireFormatGenerator.class.getSimpleName());
  private static final Marshaller<ServiceKindToServiceQueryMap> MARSHALLER = createMarshaller(ServiceKindToServiceQueryMap.class);

  @VisibleForTesting
  FileSystem fs = FileSystems.getDefault();

  private final String buildDirectory;
  private final String outputTarget;

  public WireFormatGenerator(String buildDirectory, String outputTarget) {
    this.buildDirectory = buildDirectory;
    this.outputTarget = outputTarget;
  }

  public static void main(String... args) throws IOException {
    Preconditions.checkArgument(args.length == 2, "exactly two arguments required (build directory, output target)");
    Preconditions.checkNotNull(args[0], "build directory (1st arg) must be defined");
    Preconditions.checkNotNull(args[1], "output target (2nd arg) must be defined");

    WireFormatGenerator wireFormatGenerator = new WireFormatGenerator(args[0], args[1]);
    Guice.createInjector(Stage.DEVELOPMENT).injectMembers(wireFormatGenerator);
    wireFormatGenerator.generateWireFormats();
  }

  @Inject ComponentInfoAggregator componentInfoAggregator;

  @VisibleForTesting
  void generateWireFormats() throws IOException {
    LOG.info("Scanning " + buildDirectory + " for wire formats to generate...");
    Path compiledClassesPath = fs.getPath(buildDirectory);
    if (Files.notExists(compiledClassesPath)) {
      LOG.warning("Exiting. Could not find path " + compiledClassesPath);
      return;
    }

    try (Stream<Path> classPathStream = Files.walk(compiledClassesPath)) {
      Set<Path> classPaths = classPathStream
          .filter(Files::isRegularFile)
          .filter(path -> FilenameUtils.getExtension(path.toString()).equals("class"))
          .collect(toSet());

      ComponentInfoAggregator.AggregateComponentInfo componentInfo = componentInfoAggregator.getAggregateComponentInfo(classPaths);
      Files.createDirectories(fs.getPath(outputTarget));
      for (var entry : componentInfo.getQueryInfo().asMap().entrySet()) {
        Collection<Tuple2<Class<? extends Query<?>>, Class<? extends ServiceKind>>> clientQueryInfo = entry.getValue();
        Map<String, ServiceQueryMap> serviceKindToServiceQueryMap = new HashMap<>();
        clientQueryInfo.stream()
            .collect(groupingBy(t2 -> t2._2, mapping(t2 -> t2._1, toList())))
            .forEach((serviceKind, queries) -> {
              Errors errors = new Errors();
              JavaApiTypesBuilder typesBuilder = new JavaApiTypesBuilder();
              for (Class<? extends Query<?>> queryClass : queries) {
                JavaApiSchemaIntrospector.introspectQuery(errors, queryClass, typesBuilder);
              }
              errors.throwIfHasErrors();

              serviceKindToServiceQueryMap.put(
                  serviceKind.getSimpleName(),
                  new ServiceQueryMap(typesBuilder.getQueries().entrySet().stream()
                      .collect(toMap(
                          e -> e.getKey().getSimpleName(),
                          e -> e.getValue().getWireFormat()))));
            });
        byte[] uncompressed = MARSHALLER.marshall(new ServiceKindToServiceQueryMap(serviceKindToServiceQueryMap)).toString()
            .getBytes(StandardCharsets.UTF_8);
        String clientName = entry.getKey();
        Path outputPath = fs.getPath(outputTarget, clientName + ".gz");
        try (OutputStream fos = Files.newOutputStream(outputPath)) {
          fos.write(gzip(uncompressed));
        }
      }
    }
  }

}