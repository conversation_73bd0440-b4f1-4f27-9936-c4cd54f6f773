package com.kaching.api;

import static com.google.common.base.Preconditions.checkNotNull;

import com.google.common.collect.ImmutableList;
import com.google.inject.Inject;
import com.kaching.api.JavaApiType.JavaObjectType;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.Option;
import com.kaching.platform.discovery.ServiceKind;
import com.squareup.javapoet.ClassName;

public class DefaultJavaApiTypeNamespaceTransformer implements JavaApiTypeNamespaceTransformer {

  public static final String STUB_QUERIES_PACKAGE = "com.wealthfront.stubqueries"; // TODO: Move to com.wealthfront.auto.queries
  public static final String AUTO_GEN_PACKAGE = "com.wealthfront.auto";
  private static final String TYPES_PACKAGE = "com.wealthfront.auto.types";

  @Inject JavaApiValidator validator;

  @Override
  public FullyQualifiedTypeName resolveQuery(Class<? extends ServiceKind> serviceKind, JavaApiQuerySchema query) {
    return new FullyQualifiedTypeName(getStubQueryPackage(serviceKind), ImmutableList.of(query.getQueryName()));
  }

  @Override
  public FullyQualifiedTypeName resolveObject(Class<? extends ServiceKind> serviceKind, JavaApiQuerySchema query, JavaObjectType object) {
    Errors errors = new Errors();
    ExposeType exposeType = validator.getAndValidateExposeType(errors, object).getOrThrow(errors.toString());
    errors.throwIfHasErrors();
    Option<JavaObjectType> polymorphicAncestor = object.getTopPolymorphicAncestor();
    if (polymorphicAncestor.isDefined()) {
      JavaObjectType highest = polymorphicAncestor.getOrThrow();
      return exposeType.namespace().visit(new RewriteNamespaceVisitor<>() {
        @Override
        public FullyQualifiedTypeName caseGlobal() {
          throw new IllegalArgumentException("ExposeType.GLOBAL not yet supported " + object.getEntityClass());
        }

        @Override
        public FullyQualifiedTypeName caseService() {
          return new FullyQualifiedTypeName(getTypesPackage(serviceKind), ImmutableList.of(
              concatAllClassNames(highest.getEntityClass()),
              object.getEntityClass().getSimpleName()
          ));
        }

        @Override
        public FullyQualifiedTypeName caseQuery() {
          return new FullyQualifiedTypeName(getStubQueryPackage(serviceKind), ImmutableList.of(
              query.getQueryName(),
              highest.getEntityClass().getSimpleName(),
              object.getEntityClass().getSimpleName()
          ));
        }

        @Override
        public FullyQualifiedTypeName caseDoNotCopy() {
          return new FullyQualifiedTypeName(object.getEntityClass());
        }

        @Override
        public FullyQualifiedTypeName caseInlineIntoChildren() {
          throw new IllegalArgumentException(
              "Fields of INLINE_INTO_CHILDREN classes are inlined into concrete children. " +
                  "The original class is not referenced: " + object.getEntityClass());
        }
      });
    } else {
      return exposeType.namespace().visit(new RewriteNamespaceVisitor<>() {
        @Override
        public FullyQualifiedTypeName caseGlobal() {
          throw new IllegalArgumentException("ExposeType.GLOBAL not yet supported " + object.getEntityClass());
        }

        @Override
        public FullyQualifiedTypeName caseService() {
          return new FullyQualifiedTypeName(getTypesPackage(serviceKind), ImmutableList.of(
              concatAllClassNames(object.getEntityClass())
          ));
        }

        @Override
        public FullyQualifiedTypeName caseQuery() {
          return new FullyQualifiedTypeName(getStubQueryPackage(serviceKind), ImmutableList.of(
              query.getQueryName(),
              object.getEntityClass().getSimpleName()
          ));
        }

        @Override
        public FullyQualifiedTypeName caseDoNotCopy() {
          return new FullyQualifiedTypeName(object.getEntityClass());
        }

        @Override
        public FullyQualifiedTypeName caseInlineIntoChildren() {
          throw new IllegalArgumentException("Fields of INLINE_INTO_CHILDREN classes are inlined into concrete children. " +
              "The original class is not referenced: " + object.getEntityClass());
        }
      });
    }
  }

  @Override
  public FullyQualifiedTypeName resolveEnum(Class<? extends ServiceKind> serviceKind, JavaApiQuerySchema query, JavaApiType.JavaEnumType enumSchema) {
    ExposeType exposeType = checkNotNull(enumSchema.getEnumClass().getAnnotation(ExposeType.class),
        "Class %s does not have @ExposeType", enumSchema.getEnumClass());
    return exposeType.namespace().visit(new RewriteNamespaceVisitor<>() {
      @Override
      public FullyQualifiedTypeName caseGlobal() {
        throw new IllegalArgumentException("ExposeType.GLOBAL not yet supported " + enumSchema.getEnumClass());
      }

      @Override
      public FullyQualifiedTypeName caseService() {
        return new FullyQualifiedTypeName(getTypesPackage(serviceKind), ImmutableList.of(
            concatAllClassNames(enumSchema.getEnumClass())
        ));
      }

      @Override
      public FullyQualifiedTypeName caseQuery() {
        return new FullyQualifiedTypeName(getStubQueryPackage(serviceKind), ImmutableList.of(
            query.getQueryName(),
            enumSchema.getEnumClass().getSimpleName()
        ));
      }

      @Override
      public FullyQualifiedTypeName caseDoNotCopy() {
        return new FullyQualifiedTypeName(enumSchema.getEnumClass());
      }

      @Override
      public FullyQualifiedTypeName caseInlineIntoChildren() {
        throw new IllegalArgumentException("INLINE_INTO_CHILDREN not valid for enums: " + enumSchema.getEnumClass().getSimpleName());
      }
    });
  }

  public static String getStubQueryPackage(Class<? extends ServiceKind> serviceKind) {
    return STUB_QUERIES_PACKAGE + "." + serviceKind.getSimpleName().toLowerCase();
  }

  private static String getTypesPackage(Class<? extends ServiceKind> serviceKind) {
    return TYPES_PACKAGE + "." + serviceKind.getSimpleName().toLowerCase();
  }

  private static String concatAllClassNames(Class<?> clazz) {
    return String.join("", ClassName.get(clazz).simpleNames());
  }

}
