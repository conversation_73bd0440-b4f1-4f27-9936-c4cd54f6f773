package com.kaching.api;

public interface JavaApiTypeVisitor<T> {
  
  T caseMapType(JavaApiType.JavaMapType mapType);
  
  T caseArrayType(JavaApiType.JavaArrayType arrayType);
  
  T caseSimpleType(JavaApiType.JavaSimpleType simpleType);
  
  T caseObjectType(JavaApiType.JavaObjectType objectType);
  
  T caseOptionType(JavaApiType.JavaOptionType optionType);
  
  T caseCollectionType(JavaApiType.JavaCollectionType collectionType);
  
  T caseEnumType(JavaApiType.JavaEnumType enumType);
  
  class DefaultVisitor<T> implements JavaApiTypeVisitor<T> {
    
    private final T defaultValue;

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = defaultValue;
    }

    @Override
    public T caseMapType(JavaApiType.JavaMapType mapType) {
      return defaultValue;
    }

    @Override
    public T caseArrayType(JavaApiType.JavaArrayType arrayType) {
      return defaultValue;
    }

    @Override
    public T caseSimpleType(JavaApiType.JavaSimpleType simpleType) {
      return defaultValue;
    }

    @Override
    public T caseObjectType(JavaApiType.JavaObjectType objectType) {
      return defaultValue;
    }

    @Override
    public T caseOptionType(JavaApiType.JavaOptionType optionType) {
      return defaultValue;
    }

    @Override
    public T caseCollectionType(JavaApiType.JavaCollectionType collectionType) {
      return defaultValue;
    }

    @Override
    public T caseEnumType(JavaApiType.JavaEnumType enumType) {
      return defaultValue;
    }
    
  }

}
