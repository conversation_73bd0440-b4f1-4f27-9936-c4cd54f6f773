package com.kaching.api;

import static com.kaching.api.ExposeTo.API_SERVER;
import static com.kaching.api.ExposeTo.FRONTEND;
import static com.kaching.api.JavaApiSchemaIntrospector.simplifyTypeArgs;
import static com.kaching.api.SkipValidation.JSON_ABSTRACT_POLY_FIELD_ENFORCING;
import static com.kaching.api.SkipValidation.OWNED_ENFORCING;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.reflect.TypeUtils;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.JavaApiQuerySchema.JavaQueryParameter.Flag;
import com.kaching.api.JavaApiType.JavaObjectType;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.queryengine.OwnedRequired;
import com.kaching.platform.queryengine.Query;
import com.kaching.util.id.FlexId;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

public class JavaApiValidatorImpl implements JavaApiValidator {

  @Override
  public void validateQuery(Errors errors, Class<? extends Query<?>> query, JavaApiTypesBuilder typesBuilder) {
    JavaApiQuerySchema querySchema = typesBuilder.getQueries().get(query);
    ExposeQuery annotation = query.getAnnotation(ExposeQuery.class);
    if (annotation == null) {
      errors.addMessage("Query %s has no @ExposeQuery", query);
      return;
    }
    enforceRecursiveExposeTo(errors, querySchema, typesBuilder);
    enforceNoNonPolyAbstractClassFields(errors, querySchema, typesBuilder);

    Set<ExposeTo> exposeTos = ImmutableSet.copyOf(annotation.value());

    if (exposeTos.contains(API_SERVER) || exposeTos.contains(FRONTEND)) {
      enforceApiServerConstraints(errors, querySchema, typesBuilder);
    }

    if (exposeTos.contains(FRONTEND)) {
      enforceFrontendConstraints(errors, querySchema, typesBuilder);
    }
  }

  @Override
  public void validateFrontendTypes(Errors errors, JavaApiTypesBuilder typesBuilder) {
    for (Class<?> entityClass : typesBuilder.getObjectTypes().keySet()) {
      enforceFrontendConstraintsOnClass(errors, entityClass);
      enforceNoNonPolyAbstractClassFieldOnClass(errors, entityClass);
    }
  }

  @Override
  public Option<ExposeType> getAndValidateExposeType(Errors errors, JavaObjectType objectType) {
    if (objectType.getParent().isEmpty()) {
      ExposeType annotation = objectType.getEntityClass().getAnnotation(ExposeType.class);
      if (annotation == null) {
        errors.addMessage("Json entity %s does not have @ExposeTo annotation", objectType.getEntityClass());
        return Option.none();
      }

      validateExposeType(errors, annotation, objectType);
      return Option.some(annotation);
    }

    Option<JavaObjectType> highestPolymorphic = objectType.getTopPolymorphicAncestor();
    if (highestPolymorphic.isDefined()) {
      List<JavaObjectType> descendentsWithAnnotations = highestPolymorphic.getOrThrow().getAllDescendents().stream()
          .filter(descendent -> descendent.getEntityClass().isAnnotationPresent(ExposeType.class))
          .peek(descendent -> errors.addMessage("%s is a polymorphic descendent of %s. @ExposeType should only be added to %s",
              descendent.getEntityClass(), highestPolymorphic.getOrThrow().getEntityClass(), highestPolymorphic.getOrThrow().getEntityClass()))
          .toList();
      if (!descendentsWithAnnotations.isEmpty()) {
        return Option.none();
      }
      ExposeType annotation = highestPolymorphic.getOrThrow().getEntityClass().getAnnotation(ExposeType.class);
      if (annotation == null) {
        errors.addMessage("%s is a polymorphic descendent of %s, which should have an @ExposeType annotation but doesn't",
            objectType.getEntityClass(), highestPolymorphic.getOrThrow().getEntityClass());
        return Option.none();
      }

      validateExposeType(errors, annotation, objectType);
      return Option.some(annotation);
    }
    ExposeType annotation = objectType.getEntityClass().getAnnotation(ExposeType.class);
    if (annotation == null) {
      errors.addMessage("%s must have an @ExposeType annotation (as should all the non-polymorphic @Entity classes it extends)", objectType.getEntityClass());
      return Option.none();
    }

    for (JavaObjectType ancestor : objectType.getAllAncestorsAsc()) {
      ExposeType ancestorAnnotation = ancestor.getEntityClass().getAnnotation(ExposeType.class);
      if (ancestorAnnotation == null) {
        errors.addMessage("%s is a non-polymorphic subclass of %s, which has no @ExposeType annotation (both should have one)",
            objectType.getEntityClass(), ancestor.getEntityClass());
        return Option.none();
      }
      Set<ExposeTo> onlyInChild = Sets.difference(ImmutableSet.copyOf(annotation.value()), ImmutableSet.copyOf(ancestorAnnotation.value()));
      if (!onlyInChild.isEmpty()) {
        errors.addMessage("%s has wider exposure (%s) than its parent %s (%s). Children may only be more restrictive.",
            objectType.getEntityClass(), ImmutableList.copyOf(annotation.value()), ancestor.getEntityClass(), ImmutableList.copyOf(ancestorAnnotation.value()));
        return Option.none();
      }
    }

    validateExposeType(errors, annotation, objectType);
    return Option.some(annotation);
  }

  private void validateExposeType(Errors errors, ExposeType annotation, JavaObjectType objectType) {
    if (annotation.namespace() == RewriteNamespace.INLINE_INTO_CHILDREN) {
      if (objectType.getDiscriminatorName().isDefined() || objectType.getDiscriminatorValue().isDefined()) {
        errors.addMessage("Only non-polymorphic json classes can INLINE_INTO_CHILDREN: " + objectType.getEntityClass().getSimpleName());
      }
      if (!objectType.isAbstract()) {
        errors.addMessage("Only abstract classes can INLINE_INTO_CHILDREN: " + objectType.getEntityClass().getSimpleName());
      }
    }

    if (List.of(annotation.value()).contains(FRONTEND)) {
      if (objectType.getEntityType() == EntityType.JACKSON && annotation.namespace() != RewriteNamespace.DO_NOT_COPY) {
        errors.addMessage("Jackson entities exposed with FRONTEND must be namespaced DO_NOT_COPY: " + objectType.getEntityClass().getSimpleName());
      }
    }
  }

  private void enforceApiServerConstraints(Errors errors, JavaApiQuerySchema querySchema, JavaApiTypesBuilder typesBuilder) {
    querySchema.getReturnType().getAllReferencedTypes().forEach(type -> type.visit(new JavaApiTypeVisitor.DefaultVisitor<>(Unit.unit) {
      @Override
      public Unit caseSimpleType(JavaApiType.JavaSimpleType simpleType) {
        Set<Type> possibleOffenders = new HashSet<>();
        possibleOffenders.add(simpleType.getJavaType());
        possibleOffenders.addAll(typesBuilder.getReverseOverrides(simpleType.getJavaType()));

        for (Type t : possibleOffenders) {
          Class<?> rawType = TypeUtils.getRawType(t, null);
          if (rawType.equals(Id.class) || rawType.equals(FlexId.class)) {
            String message = Strings.format("Query %s exposed to API_SERVER or FRONTEND should return ExternalId<> instead of %s", querySchema.getQueryName(),
                t);
            if (!t.equals(simpleType.getJavaType())) {
              message += " or " + simpleType.getJavaType();
            }
            errors.addMessage(message);
          }
        }
        return Unit.unit;
      }
    }));
    querySchema.getParameters()
        .stream()
        .peek(param -> enforceOwnedAnnotations(errors, querySchema, param))
        .flatMap(param -> param.getType().getAllReferencedTypes().stream())
        .forEach(type -> type.visit(new JavaApiTypeVisitor.DefaultVisitor<>(Unit.unit) {
          @Override
          public Unit caseSimpleType(JavaApiType.JavaSimpleType simpleType) {
            Set<Type> possibleOffenders = new HashSet<>();
            possibleOffenders.add(simpleType.getJavaType());
            Set<Type> reverseOverrides = typesBuilder.getReverseOverrides(simpleType.getJavaType());
            possibleOffenders.addAll(reverseOverrides);

            for (Type t : possibleOffenders) {
              Class<?> rawType = TypeUtils.getRawType(t, null);
              if (rawType.equals(Id.class)) {
                String message = Strings.format("Query %s exposed to API_SERVER or FRONTEND should accept FlexId<> or ExternalId<> instead of %s", querySchema.getQueryName(), t);
                if (!t.equals(simpleType.getJavaType())) {
                  message += " or " + simpleType.getJavaType();
                }
                errors.addMessage(message);
              }
            }
            return Unit.unit;
          }
        }));
  }

  private void enforceNoNonPolyAbstractClassFields(Errors errors, JavaApiQuerySchema querySchema, JavaApiTypesBuilder typesBuilder) {
    for (Class<?> entityClass : typesBuilder.getQueryObjectReferences(querySchema.getQueryClass())) {
      enforceNoNonPolyAbstractClassFieldOnClass(errors, entityClass);
    }
  }

  private void enforceFrontendConstraints(Errors errors, JavaApiQuerySchema querySchema, JavaApiTypesBuilder typesBuilder) {
    for (Class<?> entityClass : typesBuilder.getQueryObjectReferences(querySchema.getQueryClass())) {
      enforceFrontendConstraintsOnClass(errors, entityClass);
    }
  }

  private void enforceFrontendConstraintsOnClass(Errors errors, Class<?> entityClass) {
    for (Field field : entityClass.getDeclaredFields()) {
      Option.of(field.getAnnotation(Value.class)).ifDefined(
          value -> {
            if (value.optional() != value.nullable()) {
              errors.addMessage(String.format("Field %s in class %s has mismatched optional and nullable attributes",
                  field.getName(), entityClass.getName()));
            }
          }
      );
    }
  }

  private void enforceNoNonPolyAbstractClassFieldOnClass(Errors errors, Class<?> entityClass) {
    Set<SkipValidation> skipValidations = new HashSet<>();
    Class<?> currentClass = entityClass;

    while (currentClass != null && currentClass != Object.class) {
      ExposeType annotation = currentClass.getAnnotation(ExposeType.class);
      if (annotation != null) {
        skipValidations.addAll(Arrays.asList(annotation.skipValidation()));
      }
      currentClass = currentClass.getSuperclass();
    }

    if (skipValidations.contains(JSON_ABSTRACT_POLY_FIELD_ENFORCING)) {
      return;
    }
    for (Field field : entityClass.getDeclaredFields()) {
      Class<?> type = field.getType();
      boolean isAbstract = Modifier.isAbstract(type.getModifiers());

      Option.of(type.getAnnotation(Entity.class)).ifDefined(
          entity -> {
            if (isAbstract && (entity.discriminatorName() == null || entity.discriminatorName().isEmpty())) {
              errors.addMessage(String.format("Field %s in class %s is an abstract class without a discriminator name",
                  field.getName(), entityClass.getName()));
            }
          }
      );
    }
  }

  private void enforceRecursiveExposeTo(Errors errors, JavaApiQuerySchema querySchema, JavaApiTypesBuilder typesBuilder) {
    Set<ExposeTo> queryExposedTo = ImmutableSet.copyOf(querySchema.getQueryClass().getAnnotation(ExposeQuery.class).value());
    for (Class<?> entityClass : typesBuilder.getQueryObjectReferences(querySchema.getQueryClass())) {
      JavaObjectType entityType = typesBuilder.getObjectType(entityClass).getOrThrow();
      Errors exposeTypeErrors = new Errors();
      Option<ExposeType> maybeEntityExposeType = getAndValidateExposeType(exposeTypeErrors, entityType);
      exposeTypeErrors.getMessages().stream()
          .map(err -> Strings.format("%s. Query: %s", err, querySchema.getQueryName()))
          .forEach(errors::addMessage);
      if (maybeEntityExposeType.isEmpty()) {
        continue;
      }
      ExposeType entityExposeType = maybeEntityExposeType.getOrThrow();
      if (!ImmutableSet.copyOf(entityExposeType.value()).containsAll(queryExposedTo)) {
        errors.addMessage("Entity %s has exposure %s, which is narrower or different than query %s with exposure %s which uses it",
            entityType.getEntityClass(), ImmutableList.copyOf(entityExposeType.value()), querySchema.getQueryName(), queryExposedTo);
      }
    }
    for (Class<? extends Enum<?>> enumClass : typesBuilder.getQueryEnumReferences(querySchema.getQueryClass())) {
      ExposeType enumAnnotation = enumClass.getAnnotation(ExposeType.class);
      if (enumAnnotation == null) {
        errors.addMessage("Enum %s does not have @ExposeType. Query: %s", enumClass, querySchema.getQueryName());
        continue;
      }
      if (enumAnnotation.namespace() == RewriteNamespace.INLINE_INTO_CHILDREN) {
        errors.addMessage("INLINE_INTO_CHILDREN only applies to abstract non-polymorphic json classes: " + enumClass.getSimpleName());
      }
      if (!ImmutableSet.copyOf(enumAnnotation.value()).containsAll(queryExposedTo)) {
        errors.addMessage("Enum %s has exposure %s, which is narrower or different than query %s with exposure %s which uses it",
            enumClass, ImmutableList.copyOf(enumAnnotation.value()), querySchema.getQueryName(), queryExposedTo);
      }
    }
  }

  private void enforceOwnedAnnotations(
      Errors errors,
      JavaApiQuerySchema querySchema,
      JavaApiQuerySchema.JavaQueryParameter param) {
    SkipValidation[] skipValidations = querySchema.getQueryClass().getAnnotation(ExposeQuery.class).skipValidation();
    if (param.getFlags().contains(Flag.OWNED_ENFORCING) ||
        Arrays.stream(skipValidations).anyMatch(skip -> skip == OWNED_ENFORCING)) {
      return;
    }
    enforceOwnedAnnotations(errors, querySchema, param.getName(), param.getType(), false);
  }

  private void enforceOwnedAnnotations(
      Errors errors,
      JavaApiQuerySchema querySchema,
      String parameterName,
      JavaApiType type,
      boolean isFieldOnObjectType) {
    type.visit(new JavaApiTypeVisitor<Unit>() {

      @Override
      public Unit caseSimpleType(JavaApiType.JavaSimpleType simpleType) {
        Type javaType = simpleType.getJavaType();
        Option<Class<?>> maybeOwnedEnforcingClass = getMaybeOwnedRequiredClass(javaType);
        if (maybeOwnedEnforcingClass.isDefined()) {
          if (!isFieldOnObjectType) {
            errors.addMessage("Parameter %s of query %s is @OwnedRequired", parameterName,
                querySchema.getQueryName());
          } else if (!getAlreadyOwnedRequiredParameters(querySchema.getParameters()).contains(maybeOwnedEnforcingClass.getOrThrow())) {
            errors.addMessage("Parameter %s of query %s contains @OwnedRequired class %s", parameterName,
                querySchema.getQueryName(), javaType.getTypeName());
          }
        }
        return Unit.unit;
      }

      @Override
      public Unit caseObjectType(JavaApiType.JavaObjectType objectType) {
        if (isWfModelsClassType(objectType.getJavaType()) && !hasOwnedConstructorArguments(querySchema.getParameters())) {
          errors.addMessage(
              "Query %s contains objectType %s without additional @Owned constructor arguments",
              querySchema.getQueryName(), parameterName);
        } else {
          objectType.getFields().forEach(child -> enforceOwnedAnnotations(errors, querySchema, parameterName, child.getApiType(), true));
        }
        return Unit.unit;
      }

      @Override
      public Unit caseMapType(JavaApiType.JavaMapType mapType) {
        enforceOwnedAnnotations(errors, querySchema, parameterName, mapType.getApiKeyType(), false);
        enforceOwnedAnnotations(errors, querySchema, parameterName, mapType.getApiValueType(), false);
        return Unit.unit;
      }

      @Override
      public Unit caseArrayType(JavaApiType.JavaArrayType arrayType) {
        enforceOwnedAnnotations(errors, querySchema, parameterName, arrayType.getApiValueType(), false);
        return Unit.unit;
      }

      @Override
      public Unit caseOptionType(JavaApiType.JavaOptionType optionType) {
        enforceOwnedAnnotations(errors, querySchema, parameterName, optionType.getApiValueType(), false);
        return Unit.unit;
      }

      @Override
      public Unit caseCollectionType(JavaApiType.JavaCollectionType collectionType) {
        enforceOwnedAnnotations(errors, querySchema, parameterName, collectionType.getApiValueType(), false);
        return Unit.unit;
      }

      @Override
      public Unit caseEnumType(JavaApiType.JavaEnumType enumType) {
        return Unit.unit;
      }
    });
  }

  private Option<Class<?>> getMaybeOwnedRequiredClass(Type javaType) {
    Class<?> rawType = TypeUtils.getRawType(javaType, null);
    if (rawType.isAnnotationPresent(OwnedRequired.class)) {
      return Option.some(rawType);
    } else if (javaType instanceof ParameterizedType parameterizedType) {
      for (Type typeArg: simplifyTypeArgs(parameterizedType)) {
        if (typeArg instanceof Class<?> && ((Class<?>) typeArg).isAnnotationPresent(OwnedRequired.class)) {
          return Option.some((Class<?>) typeArg);
        }
      }
    }
    return Option.none();
  }

  private boolean isWfModelsClassType(Type javaType) {
    Class<?> rawType = TypeUtils.getRawType(javaType, null);
    return rawType.getPackageName().equals("com.wealthfront.model");
  }

  private Set<Class<?>> getAlreadyOwnedRequiredParameters(List<JavaApiQuerySchema.JavaQueryParameter> parameters) {
    return parameters.stream()
        .filter(param -> param.getFlags().contains(Flag.OWNED_ENFORCING))
        .flatMap(param -> getMaybeOwnedRequiredClass(param.getType().getJavaType()).stream())
        .collect(Collectors.toSet());
  }

  private boolean hasOwnedConstructorArguments(List<JavaApiQuerySchema.JavaQueryParameter> parameters) {
    return !parameters.stream()
        .filter(param -> param.getFlags().contains(Flag.OWNED_ENFORCING))
        .toList()
        .isEmpty();
  }

}
