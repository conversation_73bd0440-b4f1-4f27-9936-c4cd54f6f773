package com.kaching.api;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkState;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonClassDescription;
import com.fasterxml.jackson.annotation.JsonEnumDefaultValue;
import com.google.common.collect.ImmutableSet;
import com.google.inject.TypeLiteral;
import com.kaching.DefaultJsonMarshallerFactory;
import com.kaching.KachingInstantiators;
import com.kaching.api.WireFormat.JsonObject;
import com.kaching.api.WireFormat.JsonObjectField;
import com.kaching.api.WireFormat.JsonString;
import com.kaching.api.WireFormat.JsonValue;
import com.kaching.platform.common.Option;
import com.kaching.platform.converters.Converter;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.KachingServices;
import com.twolattes.json.Entity;
import com.twolattes.json.Entity.RecordStyle;
import com.twolattes.json.types.JsonType;
import com.wealthfront.util.objects.DerivedMethods;

public abstract class JavaApiType implements ApiTypeDefinition {

  private final boolean nullable;
  private final Type javaType;

  public JavaApiType(boolean nullable, Type javaType) {
    this.nullable = nullable;
    this.javaType = javaType;
  }

  enum JavaObjectTypeSource {

    LENDING("com.kaching.lending.codegen"),
    MODELS("com.wealthfront.model"),
    GLOBAL_DEFINITIONS("com.wealthfront.types.definitions"),
    DEFAULT("");

    private final String packageName;

    JavaObjectTypeSource(String packageName) {
      this.packageName = packageName;
    }

    public String getPackageName() {
      return packageName;
    }

    public static Set<JavaObjectTypeSource> getPopulatedConstructorSources() {
      return Set.of(
          MODELS,
          GLOBAL_DEFINITIONS,
          DEFAULT
      );
    }

  }

  public Type getJavaType() {
    return javaType;
  }

  public boolean isNullable() {
    return nullable;
  }

  public boolean isSimpleType() {
    return this instanceof JavaSimpleType;
  }

  @Override
  public final WireFormat getWireFormat() {
    return getWireFormatOutsideJsonContext();
  }

  public JavaObjectTypeSource getSource() {
    for (JavaObjectTypeSource source : JavaObjectTypeSource.values()) {
      String typeName = getJavaType().getTypeName();
      String packageName = typeName.substring(0, typeName.lastIndexOf('.'));
      if (packageName.equals(source.getPackageName())) {
        return source;
      }
    }
    return JavaObjectTypeSource.DEFAULT;
  }

  public abstract <T> T visit(JavaApiTypeVisitor<T> visitor);

  public Set<JavaApiType> getAllReferencedTypes() {
    Set<JavaApiType> result = new HashSet<>();
    addAllReferencedTypes(result);
    return result;
  }

  abstract JsonValue getWireFormatInJsonContext(Set<Class<?>> visitedJavaClasses);

  abstract WireFormat getWireFormatOutsideJsonContext();

  abstract void addAllReferencedTypes(Set<JavaApiType> typeSet);

  public static class JavaSimpleType extends JavaApiType {

    private static final DerivedMethods<JavaSimpleType> DERIVED_METHODS = new DerivedMethods<>(JavaSimpleType.class);

    public enum SerializedType {
      BOOLEAN,
      INTEGER,
      NUMBER,
      STRING,
      LOCAL_DATE,
      LOCAL_DATE_TIME,
      ANY_JSON_OBJECT,
      ANY_JSON_VALUE,
      UNIT,
      SERVICE_KIND_CLASS,
      SERVICE_KIND
    }

    private final SerializedType serializedType;

    public JavaSimpleType(boolean nullable, Type javaType, SerializedType serializedType) {
      super(nullable, javaType);
      this.serializedType = serializedType;
    }

    public SerializedType getSerializedType() {
      return serializedType;
    }

    @Override
    public <T> T visit(JavaApiTypeVisitor<T> visitor) {
      return visitor.caseSimpleType(this);
    }

    @Override
    JsonValue getWireFormatInJsonContext(Set<Class<?>> visitedJavaClasses) {
      switch (serializedType) {
        case NUMBER:
          return new WireFormat.JsonNumber(new WireFormat.Decimal());
        case INTEGER:
          return new WireFormat.JsonNumber(new WireFormat.Integer());
        case BOOLEAN:
          return new WireFormat.JsonBoolean();
        case STRING:
          return new WireFormat.JsonString(new WireFormat.StringSerializable());
        case LOCAL_DATE:
          return new WireFormat.JsonString(new WireFormat.LocalDate());
        case LOCAL_DATE_TIME:
          return new WireFormat.JsonString(new WireFormat.LocalDateTime());
        case ANY_JSON_OBJECT:
          return new WireFormat.JsonObject();
        case ANY_JSON_VALUE:
          return new WireFormat.JsonValue();
        case UNIT:
          return new WireFormat.JsonString(new WireFormat.StringEnum(Set.of("unit")));
        case SERVICE_KIND_CLASS:
          return new WireFormat.JsonString(new WireFormat.StringEnum(getAllServiceKindClasses()));
        case SERVICE_KIND:
          return new WireFormat.JsonString(new WireFormat.StringEnum(getAllServiceKinds()));
        default:
          throw new IllegalArgumentException("Unexpected: " + serializedType);
      }
    }

    @Override
    WireFormat getWireFormatOutsideJsonContext() {
      switch (serializedType) {
        case NUMBER:
          return new WireFormat.Decimal();
        case INTEGER:
          return new WireFormat.Integer();
        case BOOLEAN:
          return new WireFormat.JsonBoolean();
        case STRING:
          return new WireFormat.StringSerializable();
        case LOCAL_DATE:
          return new WireFormat.LocalDate();
        case LOCAL_DATE_TIME:
          return new WireFormat.LocalDateTime();
        case ANY_JSON_OBJECT:
          return new WireFormat.JsonObject();
        case ANY_JSON_VALUE:
          return new WireFormat.JsonValue();
        case UNIT:
          return new WireFormat.StringEnum(Set.of("unit"));
        case SERVICE_KIND_CLASS:
          return new WireFormat.StringEnum(getAllServiceKindClasses());
        case SERVICE_KIND:
          return new WireFormat.StringEnum(getAllServiceKinds());
        default:
          throw new IllegalArgumentException("Unexpected: " + serializedType);
      }
    }

    @Override
    void addAllReferencedTypes(Set<JavaApiType> typeSet) {
      typeSet.add(this);
    }

    @Override
    public boolean equals(Object o) {
      return DERIVED_METHODS.equals(this, o);
    }

    @Override
    public int hashCode() {
      return DERIVED_METHODS.hashCode(this);
    }

    @Override
    public String toString() {
      return DERIVED_METHODS.toString(this);
    }

    private Set<String> getAllServiceKindClasses() {
      Converter<Class<? extends ServiceKind>> converter = KachingInstantiators.createConverter(new TypeLiteral<Class<? extends ServiceKind>>() {},
          new DefaultJsonMarshallerFactory());
      return KachingServices.KINDS.stream()
          .map(converter::toString)
          .collect(toSet());
    }

    private Set<String> getAllServiceKinds() {
      Converter<ServiceKind> converter = KachingInstantiators.createConverter(new TypeLiteral<ServiceKind>() {}, new DefaultJsonMarshallerFactory());
      return KachingServices.KINDS.stream()
          .map(KachingServices::singleton)
          .map(converter::toString)
          .collect(toSet());
    }

  }

  public static class JavaObjectField {

    private static final DerivedMethods<JavaObjectField> DERIVED_METHODS = new DerivedMethods<>(JavaObjectField.class);

    private final String javaName;
    private final String apiName;
    private final Type javaType;
    private final JavaApiType apiType;
    private final boolean optional;
    private final List<Class<? extends JsonType<?, ?>>> converters;

    public JavaObjectField(String javaName, String apiName, Type javaType, JavaApiType apiType, boolean optional,
                           List<Class<? extends JsonType<?, ?>>> converters) {
      this.javaName = javaName;
      this.apiName = apiName;
      this.javaType = javaType;
      this.apiType = apiType;
      this.optional = optional;
      this.converters = converters;
    }

    public String getJavaName() {
      return javaName;
    }

    public String getApiName() {
      return apiName;
    }

    public JavaApiType getApiType() {
      return apiType;
    }

    public boolean isOptional() {
      return optional;
    }

    public Type getJavaType() {
      return javaType;
    }

    public List<Class<? extends JsonType<?, ?>>> getConverters() {
      return converters;
    }

    @Override
    public boolean equals(Object o) {
      return DERIVED_METHODS.equals(this, o);
    }

    @Override
    public int hashCode() {
      return DERIVED_METHODS.hashCode(this);
    }

    @Override
    public String toString() {
      return DERIVED_METHODS.toString(this);
    }

  }

  public static class JavaObjectType extends JavaApiType {

    private List<JavaObjectField> fields;

    private final String discriminatorName;
    private final String discriminatorValue;
    private final boolean isAbstract;

    private JavaObjectType parent;
    private final Set<JavaObjectType> children;
    private final Set<Class<?>> componentInterfaces;
    private final Map<String, Method> entityDataMethods;

    public JavaObjectType(boolean nullable,
                          Class<?> entityClass,
                          String discriminatorName,
                          String discriminatorValue,
                          boolean isAbstract) {
      super(nullable, entityClass);
      if (isAbstract) {
        checkArgument(discriminatorValue == null, "Discriminator value should not exist on " +
            "uninstantiable polymorphic json entities");
      }

      this.discriminatorName = discriminatorName;
      this.discriminatorValue = discriminatorValue;
      this.children = new HashSet<>();
      this.isAbstract = isAbstract;
      this.componentInterfaces = Arrays.stream(entityClass.getInterfaces())
          .filter(declaredInterface -> declaredInterface.isAnnotationPresent(ExposeType.class))
          .filter(EntityDataComponent.class::isAssignableFrom)
          .collect(Collectors.toSet());
      this.entityDataMethods = componentInterfaces.stream()
          .flatMap(dataInterface -> Arrays.stream(dataInterface.getMethods()))
          .collect(toMap(Method::getName, method -> method));
    }

    public Class<?> getEntityClass() {
      return (Class<?>) getJavaType();
    }

    public List<JavaObjectField> getFields() {
      return fields;
    }

    public Set<Class<?>> getComponentInterfaces() {
      return componentInterfaces;
    }

    public Map<String, Method> getEntityDataMethods() {
      return entityDataMethods;
    }

    public void setFields(List<JavaObjectField> fields) {
      checkState(this.fields == null, "Fields can only be set once");
      this.fields = fields;
    }

    public Option<String> getDiscriminatorName() {
      return Option.of(discriminatorName);
    }

    public Option<String> getDiscriminatorValue() {
      return Option.of(discriminatorValue);
    }

    public boolean isAbstract() {
      return isAbstract;
    }

    public RecordStyle getRecordStyle() {
      if (getEntityClass().isAnnotationPresent(Entity.class)) {
        return getEntityClass().getAnnotation(Entity.class).recordStyle();
      }
      throw new IllegalArgumentException("Unsupported record style: " + getJavaType().getTypeName());
    }

    public EntityType getEntityType() {
      if (getEntityClass().isAnnotationPresent(Entity.class)) {
        return EntityType.TWO_LATTES;
      }

      if (getEntityClass().isAnnotationPresent(JsonClassDescription.class)) {
        return EntityType.JACKSON;
      }

      throw new IllegalArgumentException(
          "Entity type unknown: " + getJavaType().getTypeName());
    }

    public void linkParent(JavaObjectType parent) {
      checkState(this.parent == null, "Parent already linked");
      this.parent = parent;
      this.parent.children.add(this);
    }

    public Option<JavaObjectType> getParent() {
      return Option.of(parent);
    }

    public Set<JavaObjectType> getChildren() {
      return ImmutableSet.copyOf(children);
    }

    public Set<JavaObjectType> getAllDescendents() {
      Set<JavaObjectType> allDescendents = new HashSet<>();
      allDescendents.addAll(children);
      children.forEach(child -> allDescendents.addAll(child.getAllDescendents()));
      return allDescendents;
    }

    public List<JavaObjectType> getAllAncestorsAsc() {
      List<JavaObjectType> allAncestors = new ArrayList<>();
      Option<JavaObjectType> parentPtr = getParent();
      while (parentPtr.isDefined()) {
        allAncestors.add(parentPtr.getOrThrow());
        if (parentPtr.getOrThrow().getParent().isDefined()) {
          parentPtr = parentPtr.getOrThrow().getParent();
        } else {
          break;
        }
      }
      return allAncestors;
    }

    public Option<JavaObjectType> getTopPolymorphicAncestor() {
      List<JavaObjectType> ancestorsDesc = new ArrayList<>(getAllAncestorsAsc());
      Collections.reverse(ancestorsDesc);

      return Option.from(ancestorsDesc.stream()
          .filter(ancestor -> ancestor.getDiscriminatorName().isDefined())
          .findFirst());
    }

    @Override
    public <T> T visit(JavaApiTypeVisitor<T> visitor) {
      return visitor.caseObjectType(this);
    }

    @Override
    JsonValue getWireFormatInJsonContext(Set<Class<?>> visitedJavaClasses) {
      if (visitedJavaClasses.contains(this.getEntityClass())) {
        return new WireFormat.RecursiveStubJsonValue(this.getEntityClass().getSimpleName());
      }
      visitedJavaClasses.add(this.getEntityClass());
      if (getEntityType() == EntityType.TWO_LATTES && getRecordStyle() == RecordStyle.ARRAY_UNNAMED_FIELDS) {
        List<JsonObjectField> jsonFields = fields.stream()
            .map(field -> new JsonObjectField(field.isOptional(), field.getApiType().isNullable(), field.getApiType().getWireFormatInJsonContext(visitedJavaClasses)))
            .collect(toList());
        visitedJavaClasses.remove(this.getEntityClass());
        return new WireFormat.TypedJsonTuple(jsonFields);
      }
      if (discriminatorName == null) {
        WireFormat.FixedKeysJsonObject result = new WireFormat.FixedKeysJsonObject(new HashMap<>());
        List<JavaObjectType> ancestors = getAllAncestorsAsc();
        Collections.reverse(ancestors);
        for (JavaObjectType ancestor : ancestors) {
          for (JavaObjectField field : ancestor.getFields()) {
            result.withField(field.getApiName(), field.isOptional(), field.getApiType().isNullable(), field.getApiType().getWireFormatInJsonContext(visitedJavaClasses));
          }
        }
        for (JavaObjectField field : fields) {
          result.withField(field.getApiName(), field.isOptional(), field.getApiType().isNullable(), field.getApiType().getWireFormatInJsonContext(visitedJavaClasses));
        }
        visitedJavaClasses.remove(this.getEntityClass());
        return result;
      }
      WireFormat.PolymorphicFixedKeysJsonObject wireFormat =
          WireFormat.PolymorphicFixedKeysJsonObject.create(discriminatorName);

      addFieldsFromSelfAndAncestors(wireFormat, this, visitedJavaClasses);
      for (JavaObjectType descendent : getAllDescendents()) {
        addFieldsFromSelfAndAncestors(wireFormat, descendent, visitedJavaClasses);
      }

      visitedJavaClasses.remove(this.getEntityClass());
      return wireFormat;
    }

    @Override
    WireFormat getWireFormatOutsideJsonContext() {
      return getWireFormatInJsonContext(new HashSet<>());
    }

    @Override
    void addAllReferencedTypes(Set<JavaApiType> typeSet) {
      if (typeSet.contains(this)) {
        return;
      }
      typeSet.add(this);
      for (JavaObjectField field : getFields()) {
        field.getApiType().addAllReferencedTypes(typeSet);
      }
      for (JavaObjectType parent : getParent()) {
        parent.addAllReferencedTypes(typeSet);
      }
      for (JavaObjectType child : getChildren()) {
        child.addAllReferencedTypes(typeSet);
      }
    }

    private void addFieldsFromSelfAndAncestors(WireFormat.PolymorphicFixedKeysJsonObject wireFormat, JavaObjectType javaType, Set<Class<?>> visitedJavaClasses) {
      if (javaType.isAbstract()) {
        return;
      }
      String discriminatorValue = javaType.getDiscriminatorValue().getOrThrow();
      for (JavaObjectField field : javaType.getFields()) {
        wireFormat.withField(discriminatorValue, field.getApiName(), field.isOptional(), field.getApiType().isNullable(), field.getApiType().getWireFormatInJsonContext(visitedJavaClasses));
      }
      for (JavaObjectType ancestor : javaType.getAllAncestorsAsc()) {
        for (JavaObjectField field : ancestor.getFields()) {
          wireFormat.withField(discriminatorValue, field.getApiName(), field.isOptional(), field.getApiType().isNullable(), field.getApiType().getWireFormatInJsonContext(visitedJavaClasses));
        }
      }
    }

  }

  public static class JavaEnumType extends JavaApiType {

    private static final DerivedMethods<JavaEnumType> DERIVED_METHODS = new DerivedMethods<>(JavaEnumType.class);

    public JavaEnumType(boolean nullable, Class<? extends Enum<?>> enumClass) {
      super(nullable, enumClass);
    }

    public Class<? extends Enum<?>> getEnumClass() {
      return (Class<? extends Enum<?>>) getJavaType();
    }

    public List<String> getEnumConstantNames() {
      return getEnumConstants().stream()
          .map(Enum::name)
          .collect(toList());
    }

    public List<? extends Enum<?>> getEnumConstants() {
      return Arrays.stream(getEnumClass().getEnumConstants())
          .filter(val -> {
            try {
              return !getEnumClass().getField(val.name()).isAnnotationPresent(JsonEnumDefaultValue.class);
            } catch (Exception ex) {
              return true;
            }
          })
          .collect(toList());
    }

    @Override
    public boolean equals(Object o) {
      return DERIVED_METHODS.equals(this, o);
    }

    @Override
    public int hashCode() {
      return DERIVED_METHODS.hashCode(this);
    }

    @Override
    public String toString() {
      return DERIVED_METHODS.toString(this);
    }

    @Override
    public <T> T visit(JavaApiTypeVisitor<T> visitor) {
      return visitor.caseEnumType(this);
    }

    @Override
    JsonValue getWireFormatInJsonContext(Set<Class<?>> visitedJavaClasses) {
      return new WireFormat.JsonString(new WireFormat.StringEnum(ImmutableSet.copyOf(getEnumConstantNames())));
    }

    @Override
    WireFormat getWireFormatOutsideJsonContext() {
      return new WireFormat.StringEnum(ImmutableSet.copyOf(getEnumConstantNames()));
    }

    @Override
    void addAllReferencedTypes(Set<JavaApiType> typeSet) {
      typeSet.add(this);
    }

  }

  public static class JavaOptionType extends JavaApiType {

    private static final DerivedMethods<JavaOptionType> DERIVED_METHODS = new DerivedMethods<>(JavaOptionType.class);

    private final Type javaValueType;
    private final JavaApiType apiValueType;

    public JavaOptionType(Type declaredJavaType, Type javaValueType, JavaApiType apiValueType) {
      super(false, declaredJavaType);
      checkArgument(apiValueType.isNullable(), "The parameterized value of an Option<> type should be nullable: %s, %s", declaredJavaType, apiValueType);
      this.javaValueType = javaValueType;
      this.apiValueType = apiValueType;
    }

    public Type getJavaValueType() {
      return javaValueType;
    }

    public JavaApiType getApiValueType() {
      return apiValueType;
    }

    @Override
    public <T> T visit(JavaApiTypeVisitor<T> visitor) {
      return visitor.caseOptionType(this);
    }

    @Override
    JsonValue getWireFormatInJsonContext(Set<Class<?>> visitedJavaClasses) {
      throw new IllegalArgumentException("Fields wrapped in Option<> inside of JSON entities not supported: " + getJavaType());
    }

    @Override
    WireFormat getWireFormatOutsideJsonContext() {
      return apiValueType.getWireFormat();
    }

    @Override
    void addAllReferencedTypes(Set<JavaApiType> typeSet) {
      if (typeSet.contains(this)) {
        return;
      }
      typeSet.add(this);
      getApiValueType().addAllReferencedTypes(typeSet);
    }

    @Override
    public boolean equals(Object o) {
      return DERIVED_METHODS.equals(this, o);
    }

    @Override
    public int hashCode() {
      return DERIVED_METHODS.hashCode(this);
    }

    @Override
    public String toString() {
      return DERIVED_METHODS.toString(this);
    }

  }

  public static class JavaMapType extends JavaApiType {

    private static final DerivedMethods<JavaMapType> DERIVED_METHODS = new DerivedMethods<>(JavaMapType.class);

    private final Type javaKeyType;
    private final Type javaValueType;

    private final JavaApiType apiKeyType;
    private final JavaApiType apiValueType;

    public JavaMapType(boolean nullable,
                       Type declaredJavaType,
                       Type javaKeyType,
                       Type javaValueType,
                       JavaApiType apiKeyType,
                       JavaApiType valueType) {
      super(nullable, declaredJavaType);
      this.javaKeyType = javaKeyType;
      this.javaValueType = javaValueType;
      this.apiKeyType = apiKeyType;
      this.apiValueType = valueType;
    }

    public JavaApiType getApiKeyType() {
      return apiKeyType;
    }

    public JavaApiType getApiValueType() {
      return apiValueType;
    }

    public Type getJavaKeyType() {
      return javaKeyType;
    }

    public Type getJavaValueType() {
      return javaValueType;
    }

    @Override
    public boolean equals(Object o) {
      return DERIVED_METHODS.equals(this, o);
    }

    @Override
    public int hashCode() {
      return DERIVED_METHODS.hashCode(this);
    }

    @Override
    public String toString() {
      return DERIVED_METHODS.toString(this);
    }

    @Override
    public <T> T visit(JavaApiTypeVisitor<T> visitor) {
      return visitor.caseMapType(this);
    }

    @Override
    JsonObject getWireFormatInJsonContext(Set<Class<?>> visitedJavaClasses) {
      JsonValue keyFormat = apiKeyType.getWireFormatInJsonContext(visitedJavaClasses);
      if (!(keyFormat instanceof JsonString)) {
        keyFormat = new WireFormat.JsonString(keyFormat);
      }
      return new WireFormat.JsonMap((JsonString) keyFormat, apiValueType.getWireFormatInJsonContext(visitedJavaClasses));
    }

    @Override
    WireFormat getWireFormatOutsideJsonContext() {
      return getWireFormatInJsonContext(new HashSet<>());
    }

    @Override
    void addAllReferencedTypes(Set<JavaApiType> typeSet) {
      if (typeSet.contains(this)) {
        return;
      }
      typeSet.add(this);
      getApiKeyType().addAllReferencedTypes(typeSet);
      getApiValueType().addAllReferencedTypes(typeSet);
    }

  }

  public static class JavaCollectionType extends JavaApiType {

    private static final DerivedMethods<JavaCollectionType> DERIVED_METHODS = new DerivedMethods<>(JavaCollectionType.class);

    private final JavaApiType apiValueType;
    private final Type javaValueType;

    public JavaCollectionType(boolean nullable, Type declaredJavaType, Type javaValueType, JavaApiType apiValueType) {
      super(nullable, declaredJavaType);
      this.javaValueType = javaValueType;
      this.apiValueType = apiValueType;
    }

    public JavaApiType getApiValueType() {
      return apiValueType;
    }

    public Type getJavaValueType() {
      return javaValueType;
    }

    @Override
    public boolean equals(Object o) {
      return DERIVED_METHODS.equals(this, o);
    }

    @Override
    public int hashCode() {
      return DERIVED_METHODS.hashCode(this);
    }

    @Override
    public String toString() {
      return DERIVED_METHODS.toString(this);
    }

    @Override
    public <T> T visit(JavaApiTypeVisitor<T> visitor) {
      return visitor.caseCollectionType(this);
    }

    @Override
    JsonValue getWireFormatInJsonContext(Set<Class<?>> visitedJavaClasses) {
      return new WireFormat.JsonArray(apiValueType.getWireFormatInJsonContext(visitedJavaClasses));
    }

    @Override
    WireFormat getWireFormatOutsideJsonContext() {
      return getWireFormatInJsonContext(new HashSet<>());
    }

    @Override
    void addAllReferencedTypes(Set<JavaApiType> typeSet) {
      if (typeSet.contains(this)) {
        return;
      }
      typeSet.add(this);
      getApiValueType().addAllReferencedTypes(typeSet);
    }

  }

  public static class JavaArrayType extends JavaApiType {

    private static final DerivedMethods<JavaArrayType> DERIVED_METHODS = new DerivedMethods<>(JavaArrayType.class);

    private final JavaApiType apiValueType;
    private final Type javaValueType;

    public JavaArrayType(boolean nullable, Type declaredJavaType, Type javaValueType, JavaApiType apiValueType) {
      super(nullable, declaredJavaType);
      this.javaValueType = javaValueType;
      this.apiValueType = apiValueType;
    }

    public JavaApiType getApiValueType() {
      return apiValueType;
    }

    public Type getJavaValueType() {
      return javaValueType;
    }

    @Override
    public boolean equals(Object o) {
      return DERIVED_METHODS.equals(this, o);
    }

    @Override
    public int hashCode() {
      return DERIVED_METHODS.hashCode(this);
    }

    @Override
    public String toString() {
      return DERIVED_METHODS.toString(this);
    }

    @Override
    public <T> T visit(JavaApiTypeVisitor<T> visitor) {
      return visitor.caseArrayType(this);
    }

    @Override
    JsonValue getWireFormatInJsonContext(Set<Class<?>> visitedJavaClasses) {
      return new WireFormat.JsonArray(apiValueType.getWireFormatInJsonContext(visitedJavaClasses));
    }

    @Override
    WireFormat getWireFormatOutsideJsonContext() {
      return getWireFormatInJsonContext(new HashSet<>());
    }

    @Override
    void addAllReferencedTypes(Set<JavaApiType> typeSet) {
      if (typeSet.contains(this)) {
        return;
      }
      typeSet.add(this);
      getApiValueType().addAllReferencedTypes(typeSet);
    }

  }

}
