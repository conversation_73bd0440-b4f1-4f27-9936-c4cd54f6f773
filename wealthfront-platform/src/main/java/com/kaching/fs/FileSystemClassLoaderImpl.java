package com.kaching.fs;

import static com.kaching.platform.common.logging.Log.getLog;
import static java.util.Collections.emptySet;
import static org.objectweb.asm.ClassReader.SKIP_DEBUG;
import static org.objectweb.asm.ClassReader.SKIP_FRAMES;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.FileSystem;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashSet;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.apache.commons.io.FilenameUtils;
import org.objectweb.asm.ClassReader;
import org.objectweb.asm.ClassVisitor;
import org.objectweb.asm.Opcodes;

import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;

public class FileSystemClassLoaderImpl implements FileSystemClassLoader {

  private static final Log log = getLog(FileSystemClassLoaderImpl.class);

  @Override
  public Set<Class<?>> walkDirectoryForClasses(FileSystem fs, String buildDirectory, Predicate<Class<?>> filterClassBy) throws IOException {
    log.info("Scanning " + buildDirectory + " for classes...");
    Path compiledClassesPath = fs.getPath(buildDirectory);
    if (Files.notExists(compiledClassesPath)) {
      log.warn("Exiting. Could not find path " + compiledClassesPath);
      return emptySet();
    }

    Set<Class<?>> classes = new HashSet<>();
    Set<Path> classPaths = Files.walk(compiledClassesPath)
        .filter(Files::isRegularFile)
        .filter(path -> FilenameUtils.getExtension(path.toString()).equals("class"))
        .collect(Collectors.toSet());
    for (Path classPath : classPaths) {
      try (InputStream in = Files.newInputStream(classPath)) {
        Class<?> clazz = getClassForPath(in);

        if (filterClassBy.test(clazz)) {
          classes.add(clazz);
        }
      } catch (ClassNotFoundException e) {
        throw new RuntimeException(Strings.format("Unable to get class for path: %s", classPath.toString()), e);
      }
    }

    return classes;
  }

  private Class<?> getClassForPath(InputStream in) throws IOException, ClassNotFoundException {
    ClassNameExtractor classNameExtractor = new ClassNameExtractor();
    new ClassReader(in).accept(classNameExtractor, SKIP_FRAMES | SKIP_DEBUG);
    String classFileName = classNameExtractor.getClassName();
    return Class.forName(classFileName);
  }

  private static class ClassNameExtractor extends ClassVisitor {

    private String className;

    ClassNameExtractor() {
      super(Opcodes.ASM9);
    }

    @Override
    public void visit(int version, int access, String name, String signature, String superName, String[] interfaces) {
      this.className = name.replace('/', '.');
    }

    public String getClassName() {
      return className;
    }
  }

}
