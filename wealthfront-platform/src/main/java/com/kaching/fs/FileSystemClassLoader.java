package com.kaching.fs;

import java.io.IOException;
import java.nio.file.FileSystem;
import java.util.Set;
import java.util.function.Predicate;

import com.google.inject.ImplementedBy;

@ImplementedBy(FileSystemClassLoaderImpl.class)
public interface FileSystemClassLoader {

  Set<Class<?>> walkDirectoryForClasses(FileSystem fs, String buildDirectory, Predicate<Class<?>> filterClassBy) throws IOException;

}
