package com.kaching.fs;

import java.io.IOException;
import java.nio.file.FileSystem;
import java.util.Set;

import com.google.inject.ImplementedBy;
import com.kaching.api.EntityType;
import com.kaching.api.ExposeTo;

@ImplementedBy(ExposeEntitiesClassLoaderImpl.class)
public interface ExposeEntitiesClassLoader {

  Set<Class<?>> getExposedTwolattesFrontendEntities(
      FileSystem fs,
      String buildDirectory)
      throws IOException;

  Set<Class<?>> getExposedEntities(FileSystem fs, String buildDirectory) throws IOException;

  Set<Class<?>> getExposedEntities(
      FileSystem fs,
      String buildDirectory,
      Set<ExposeTo> exposeTo,
      Set<EntityType> entityTypes)
      throws IOException;

}
