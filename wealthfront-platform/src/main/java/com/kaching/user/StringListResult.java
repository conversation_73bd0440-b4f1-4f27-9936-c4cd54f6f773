package com.kaching.user;

import java.util.List;

import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;
import com.wealthfront.util.objects.DerivedMethods;

@Entity
@ExposeType(value = { ExposeTo.BACKEND }, namespace = RewriteNamespace.DO_NOT_COPY)
public class StringListResult extends AbstractBackendResult<StringListResult, List<String>> {

  private static final DerivedMethods<StringListResult> DERIVED_METHODS = new DerivedMethods<>(StringListResult.class);

  @Value
  public List<String> result;

  public StringListResult() {/*json*/}

  @Override
  public StringListResult parent() {
    return this;
  }

  @Override
  public StringListResult withResult(List<String> strings) {
    this.result = strings;
    return this;
  }

  @Override
  public List<String> getResult() {
    return result;
  }

  @Override
  public int hashCode() {
    return DERIVED_METHODS.hashCode(this);
  }

  @Override
  public boolean equals(Object obj) {
    return DERIVED_METHODS.equals(this, obj);
  }

}
