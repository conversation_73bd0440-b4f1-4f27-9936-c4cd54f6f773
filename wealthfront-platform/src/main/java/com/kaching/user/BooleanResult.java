package com.kaching.user;

import static com.kaching.api.ExposeTo.API_SERVER;
import static com.kaching.api.ExposeTo.BACKEND;
import static com.kaching.api.ExposeTo.LOCAL;
import static com.kaching.api.ExposeTo.TAOS;
import static com.kaching.api.ExposeType.RewriteNamespace.DO_NOT_COPY;

import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.util.objects.DerivedMethods;

@Entity
@ExposeType(value = { LOCAL, BACKEND, TAOS, API_SERVER }, namespace = DO_NOT_COPY)
public class BooleanResult extends AbstractBackendResult<BooleanResult, Boolean> {

  private static final DerivedMethods<BooleanResult> DERIVED_METHODS = new DerivedMethods<>(BooleanResult.class);

  @Value
  public Boolean result;

  public BooleanResult() {}

  @Override
  public BooleanResult parent() {
    return this;
  }

  @Override
  public BooleanResult withResult(Boolean result) {
    this.result = result;
    return this;
  }

  @Override
  public Boolean getResult() {
    return result;
  }

  @Override
  public Option<Boolean> getOptionResult() {
    return Option.of(result);
  }

  @Override
  public String toString() {
    return Strings.format("result: %s, errors %s", result, getErrors().toString());
  }

  @Override
  public int hashCode() {
    return DERIVED_METHODS.hashCode(this);
  }

  @Override
  public boolean equals(Object obj) {
    return DERIVED_METHODS.equals(this, obj);
  }

}
