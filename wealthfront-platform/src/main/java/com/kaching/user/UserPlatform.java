package com.kaching.user;

import static com.kaching.api.ExposeTo.API_SERVER;
import static com.kaching.api.ExposeTo.BACKEND;
import static com.kaching.api.ExposeTo.LOCAL;
import static com.kaching.api.ExposeTo.TAOS;
import static com.kaching.api.ExposeType.RewriteNamespace.DO_NOT_COPY;

import com.kaching.api.ExposeType;

@ExposeType(value = { LOCAL, BACKEND, TAOS, API_SERVER }, namespace = DO_NOT_COPY)
public enum UserPlatform {

  /**
   * On a native android app
   */
  ANDROID,

  /**
   * On a native iOS app
   */
  IOS,

  /**
   * In a browser on iOS
   */
  MOBILE_WEB_IOS,

  /**
   * In a browser on Android
   */
  MOBILE_WEB_ANDROID,

  /**
   * In a browser / web view launched by a native iOS app
   */
  MOBILE_WEB_IOS_REFERRED,

  /**
   * In a browser / web view launched by a native Android app
   */
  MOBILE_WEB_ANDROID_REFERRED,

  /**
   * In a browser on an unidentified mobile platform
   */
  MOBILE_WEB_OTHER,

  /**
   * In a browser on a desktop computer
   */
  DESKTOP_WEB,

  /**
   * In a browser on an unidentified platform
   */
  OTHER_WEB,

}
