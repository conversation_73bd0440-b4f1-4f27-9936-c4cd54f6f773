package com.kaching.user;

import com.kaching.platform.functional.Unit;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;
import com.wealthfront.util.objects.DerivedMethods;

@Entity
@ExposeType(value = { ExposeTo.BACKEND }, namespace = RewriteNamespace.DO_NOT_COPY)
public class UnitResult extends AbstractBackendResult<UnitResult, Unit> {

  private static final DerivedMethods<UnitResult> DERIVED_METHODS = new DerivedMethods<>(UnitResult.class);

  @Value(type = Unit.JsonType.class)
  public Unit result;

  public UnitResult() {/*json*/}

  @Override
  public UnitResult parent() {
    return this;
  }

  @Override
  public UnitResult withResult(Unit result) {
    this.result = result;
    return this;
  }

  @Override
  public Unit getResult() {
    return result;
  }

  @Override
  public int hashCode() {
    return DERIVED_METHODS.hashCode(this);
  }

  @Override
  public boolean equals(Object obj) {
    return DERIVED_METHODS.equals(this, obj);
  }

}
