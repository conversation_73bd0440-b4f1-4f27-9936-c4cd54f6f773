package com.kaching.user;

import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;
import com.wealthfront.util.objects.DerivedMethods;

@Entity
@ExposeType(value = { ExposeTo.BACKEND, ExposeTo.TAOS }, namespace = RewriteNamespace.DO_NOT_COPY)
public class StringResult extends AbstractBackendResult<StringResult, String> {

  private static final DerivedMethods<StringResult> DERIVED_METHODS = new DerivedMethods<>(StringResult.class);

  @Value
  public String result;

  public StringResult() {/*json*/}

  @Override
  public StringResult parent() {
    return this;
  }

  @Override
  public StringResult withResult(String result) {
    this.result = result;
    return this;
  }

  @Override
  public String getResult() {
    return result;
  }

  @Override
  public int hashCode() {
    return DERIVED_METHODS.hashCode(this);
  }

  @Override
  public boolean equals(Object obj) {
    return DERIVED_METHODS.equals(this, obj);
  }

}
