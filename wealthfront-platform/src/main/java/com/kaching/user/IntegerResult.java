package com.kaching.user;

import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.api.ExposeTo;
import com.wealthfront.util.objects.DerivedMethods;

@Entity
@ExposeType(value = { ExposeTo.BACKEND }, namespace = RewriteNamespace.DO_NOT_COPY)
public class IntegerResult extends AbstractBackendResult<IntegerResult, Integer> {

  private static final DerivedMethods<IntegerResult> DERIVED_METHODS = new DerivedMethods<>(IntegerResult.class);

  @Value
  public Integer result;

  public IntegerResult() {/*json*/}

  @Override
  public IntegerResult parent() {
    return this;
  }

  @Override
  public IntegerResult withResult(Integer result) {
    this.result = result;
    return this;
  }

  @Override
  public Integer getResult() {
    return result;
  }

  @Override
  public int hashCode() {
    return DERIVED_METHODS.hashCode(this);
  }

  @Override
  public boolean equals(Object obj) {
    return DERIVED_METHODS.equals(this, obj);
  }

}
